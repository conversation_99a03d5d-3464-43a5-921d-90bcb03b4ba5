import logging
from logging.config import dictConfig

from config.config import DevConfig, config
from contextvars import ContextVar
import uuid
from pkg import folder_util as folder


from pythonjsonlogger.jsonlogger import <PERSON><PERSON><PERSON><PERSON>atter
from datetime import datetime, timedelta, timezone

TZ = timezone(timedelta(hours=7))

class CustomJsonFormatter(JsonFormatter):
    def formatTime(self, record, datefmt=None):
        dt = datetime.fromtimestamp(record.created, tz=TZ)
        return dt.strftime("%Y-%m-%dT%H:%M:%S.%f%z")

class CorrelationIdFilter(logging.Filter):
    def filter(self, record):
        record.correlation_id = correlation_id_var.get()  
        return True
    
    
correlation_id_var = ContextVar("correlation_id", default="-")
    
def set_correlation_id():
    if correlation_id_var.get() == "-":
        correlation_id_var.set(str(uuid.uuid4()))


handlers = ["default", "rotating_file"]
if config.ENV_STATE == "prod":
    handlers = ["default", "rotating_file"]

homeDir = folder.get_home_directory()
folder.check_or_create_directory(homeDir)

def configure_logging() -> None:
    dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "filters": {
                "correlation_id": {
                    "()": CorrelationIdFilter,
                },
            },
            "formatters": {
                "console": {
                    "class": "logging.Formatter",
                    "datefmt": "%Y-%m-%dT%H:%M:%S.%f",
                    "format": "(%(correlation_id)s) %(name)s:%(lineno)d - %(message)s",
                },
                
                "file": {
                    #"class": "pythonjsonlogger.jsonlogger.JsonFormatter",
                    #"datefmt": "%Y-%m-%dT%H:%M:%S",
                    "()" : CustomJsonFormatter,
                    "format": "%(asctime)s.%(msecs)03d  %(levelname)s %(correlation_id)s %(name)s %(lineno)d %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "class": "rich.logging.RichHandler", 
                    "level": "DEBUG",
                    "formatter": "console",
                    "filters": ["correlation_id"]
                },
                "rotating_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": "DEBUG",
                    "formatter": "file",
                    "filename": f"{homeDir}/ld_claims.log",
                    "maxBytes": 1024 * 1024 *30,  # 1 MB
                    "backupCount": 2,
                    "encoding": "utf8",
                    "filters": ["correlation_id"]
                },
            },
            "loggers": {
                "": {
                    "handlers": handlers,
                    "level": "DEBUG" if isinstance(config, DevConfig) else "INFO",
                    "propagate": False,
                },
            },
        }
    )
