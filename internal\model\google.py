from pydantic import BaseModel, Field

class Installed(BaseModel):
    auth_uri: str
    client_id: str
    client_secret: str
    project_id: str
    redirect_uris: list[str]
    auth_provider_x509_cert_url: str

class CredentialsState(BaseModel):
    installed: Installed

class TokensState(BaseModel):
    access_token: str
    expiry_date: int
    refresh_token: str
    scope: str
    token_type: str

class GoogleState(BaseModel):
    id: str
    email: str
    api_key: str = Field(alias="apiKey")
    credentials: CredentialsState = Field(alias="credentials")
    tokens: TokensState = Field(alias="tokens")
    active: bool = Field(default=False)
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")