import logging
from pathlib import Path
import subprocess
import time
from typing import Optional
logger = logging.getLogger(__name__)



def merge_clips(left_video_path: str, 
                right_video_paths: str,
                workspace_dir: str,
                video_id: str,
                speed: float = 1.0,
                watermarks: Optional[dict] = None) -> str:
  
  logger.info(f"merge_clips: {left_video_path}, {right_video_paths}") 
  start_time = time.time()
  
  cmd_str = f"""
    ffmpeg 
      -i left.mp4 
      -stream_loop -1 
      -i right.mp4 
      -filter_complex "[0:v]scale=iw*1.15:ih*1.15,format=yuva444p,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(gt(X,W-108),255*(W-X)/108,255)'[L];[1:v]scale=iw*1.15:ih*1.15,format=yuva444p,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(lt(X,108),255*X/108,255)'[R];color=c=black@0:size=1080x1920:d=1[BG];[BG][L]overlay=0:(H-h)/2[tmp];[tmp][R]overlay=W-w:(H-h)/2,format=yuv420p[v]" 
      -map "[v]" -map 0:a -c:v libx264 -crf 22 -preset fast -c:a copy -shortest -metadata title="" -metadata artist="" -metadata album_artist="" -metadata album="" -metadata date="" -metadata track="" -metadata genre="" -metadata publisher="" -metadata encoded_by="" -metadata copyright="" -metadata composer="" -metadata performer="" -metadata TIT1="" -metadata TIT3="" -metadata disc="" -metadata TKEY="" -metadata TBPM="" -metadata language="eng" -metadata encoder="" output.mp4
  """
  cmd_str = cmd_str.replace("left.mp4", left_video_path)
  cmd_str = cmd_str.replace("right.mp4", right_video_paths)
  cmd_str = cmd_str.replace("output.mp4", f"{workspace_dir}/{video_id}.mp4")
  process = subprocess.Popen(cmd_str,shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
  while True:
    line = process.stdout.readline()
    if not line:
      break
    line = line.strip()
    logger.info(line)
  end_time = time.time()
  logger.info(f"merge_clips completed in {end_time - start_time} seconds")
  process.stdout.close()
  process.wait()
  if process.returncode != 0:
    logger.error(f"Merge clips failed with return code {process.returncode}")
    raise Exception(f"Merge clips failed with return code {process.returncode}")
  else:
    logger.info("Merge clips completed successfully.")
    
    
merge_clips(left_video_path="./videotest/Y0K7JujtljA_left.mp4",
            right_video_paths="./videotest/YC-NIPSMT7U_right.mp4",
            workspace_dir="./videotest",
            video_id="test")
