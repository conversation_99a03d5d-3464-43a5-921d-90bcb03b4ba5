import logging
import ffmpeg
from pydantic import BaseModel
from typing import Optional
logger = logging.getLogger(__name__)

class VideoResolution(BaseModel):
    width: int
    height: int

async def get_video_info(input_path: str) -> tuple[str, Optional[VideoResolution], Optional[Exception]]:
    """
    Get video resolution using ffprobe.
    
    Args:
        input_path: Path to input video file
        
    Returns:
        Tuple containing video resolution and an exception if any error occurs
    """
    try:
        probe = ffmpeg.probe(input_path)

        video_streams = [stream for stream in probe["streams"] if stream["codec_type"] == "video"]
        if not video_streams:
            return None, Exception("No video streams found")
        codec = video_streams[0]["codec_name"]
        return codec, VideoResolution(width=video_streams[0]["width"], 
                            height=video_streams[0]["height"]), None
    except Exception as e:
        return  None, None, e
    
async def check_valid_video(input_path: str) -> tuple[bool, Optional[Exception]]:
    codec, resolution, error = await get_video_info(input_path)
    if error:
        return False, error
    if not codec or not resolution:
        return False, Exception("Invalid video")
    if codec == "hevc":
        return False, Exception("HEVC codec is not supported")
    expected_ratio = 9 / 16
    tolerance = 0.05  # Cho phép sai số 5%
    aspect_ratio = resolution.width / resolution.height
    is_portrait = resolution.height > resolution.width
    is_short = is_portrait and abs(aspect_ratio - expected_ratio) <= tolerance
    if is_short:
        return True, None
    return False, Exception("Video is not a YouTube Short")