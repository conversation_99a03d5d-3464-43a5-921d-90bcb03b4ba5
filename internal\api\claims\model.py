from enum import Enum
from typing import Optional
from pydantic import BaseModel, Field

class ProfileAction(str, Enum):
    DEFAULT = "default"
    PARENT = "parent"

class RenderMode(BaseModel):
    id: str = Field(alias="id")
    name: Optional[str] = Field(alias="name")
    ffmpeg: Optional[str] = Field(alias="ffmpeg")
    note: Optional[str] = Field(alias="note")
    active: bool = Field(alias="active")
class TaskProfileState(BaseModel):
    id: str
    account_id: str = Field(alias="accountId")
    email: str
    profile_name: Optional[str] = Field(alias="profileName", default=None)
    channel: Optional[str] = Field(alias="channel", default=None)
    channel_name: Optional[str] = Field(alias="channelName", default=None)
    is_copyright: bool = Field(alias="isCopyright")
    enable_claim: bool = Field(alias="enableClaim")
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    resume_at: Optional[str] = Field(alias="resumeAt", default=None)
    group_id: str = Field(alias="groupId")
    music_id: Optional[str] = Field(alias="musicId", default=None)
    action: Optional[ProfileAction] = Field(alias="action", default=None)
    
class TaskProfileChildrenState(BaseModel):
    id: str
    email: str
    channel: str
    channel_name: str = Field(alias="channelName")
    default_title : Optional[str] = Field(alias="defaultTitle", default=None)
    render_mode: Optional[RenderMode] = Field(alias="RenderMode", default=None)
   
class TaskMusicItem(BaseModel):
    id: str
    name: str
    url: str
    music_url: Optional[str] = Field(alias="musicUrl", default=None)
    is_error: bool = Field(alias="isError")
    active: bool
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    group_id: str = Field(alias="groupId") 
    
class VideoStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    DONE = "done"
    DOWNLOAD_ERROR = "download_error"
    RENDER_ERROR = "render_error"
    UPLOAD_ERROR = "upload_error"
    COPYRIGHT = "copyright"
    NOTFOUND = "notfound"
    NOT_SHORT = "notshort"
    
class TaskVideoItem(BaseModel):
    id: str
    name: Optional[str] = None
    description: Optional[str] = None
    source_url: str = Field(alias="sourceUrl")
    render_url: Optional[str] = Field(alias="renderUrl", default=None)
    size: Optional[int] = None
    duration: Optional[int] = None
    is_copyright: bool = Field(alias="isCopyright")
    status: VideoStatus
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    channel_id: Optional[str] = Field(alias="channelId", default=None)

class LogStatus(str, Enum):
    PROCESSING = "processing"
    DONE = "done"
    ERROR = "error"
    COPYRIGHT = "copyright"
    TIMEOUT = "timeout"

class TaskLogItem(BaseModel):
    id: str
    date: str
    youtube_id: Optional[str] = Field(alias="youtubeId", default=None)
    is_claim: bool = Field(alias="isClaim")
    enable_claim: bool = Field(alias="enableClaim")
    status: LogStatus
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    profile_id: str = Field(alias="profileId")
    music_id: str = Field(alias="musicId")
    video_id: str = Field(alias="videoId")
    analytics_id: str = Field(alias="analyticsId")
    
class TaskVideo2Item(BaseModel):
    video: TaskVideoItem
    log: TaskLogItem
    

class TimeConfig(BaseModel):
    start: str
    end: str

class Setting(BaseModel):
    UPLOAD_TIME_NORMAL: TimeConfig
    UPLOAD_TIME_PRIME: TimeConfig

class TaskVideo2State(BaseModel):
    music: TaskMusicItem
    list: list[TaskVideo2Item]
    setting: Setting
    
class TaskVideo2Item(BaseModel):
    video: TaskVideoItem
    log: TaskLogItem
    
class TaskVideoState(BaseModel):
    music: TaskMusicItem
    video: TaskVideoItem
    log: TaskLogItem

class PayloadUpdateVideo(BaseModel):
    videoId: str
    status: VideoStatus
    renderUrl: Optional[str] = None
    size: Optional[int] = None
    isCopyright: Optional[bool] = None

class PayloadUpdateLog(BaseModel):
    logId: str
    status: LogStatus
    isClaim: Optional[bool] = None
    youtubeId: Optional[str] = None
    reason: Optional[str] = None