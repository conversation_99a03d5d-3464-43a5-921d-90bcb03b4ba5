[tool.poetry]
name = "py control ld claim serivce"
version = "0.1.0"
description = ""
authors = ["Loc <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.12,<3.14"
uiautomator2 = "^3.2.9"
python-json-logger = "^3.3.0"
pydantic-settings = "^2.9.1"
rich = "^14.0.0"
aiohttp = "^3.11.18"
aiofiles = "^24.1.0"
google-auth = "^2.40.1"
google-api-python-client = "^2.169.0"
google-generativeai = "^0.8.5"
google-genai = "^1.15.0"
pyinstaller = "^6.13.0"
moviepy = "^2.2.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

