import os
import zipfile
import requests
from pathlib import Path
import logging
import multiprocessing
logger = logging.getLogger(__name__)


async def setup_voice_separation():
    setup_cpu_limit_for_voice_separation()
    voice_separation_dir = Path.home() / "voice-separation"
    voice_separation_dir.mkdir(exist_ok=True)
    voice_separation_exe = voice_separation_dir / "voice-separation.exe"
    if not voice_separation_exe.exists():
        voice_separation_url = "https://github.com/tuwibu/voice-separation/releases/download/v1.0.1/voice-separation.zip"
        logger.info("Downloading voice separation...")
        response = requests.get(voice_separation_url)
        zip_path = voice_separation_dir / "voice-separation.zip"
        zip_path.write_bytes(response.content)
        logger.info("Extracting voice separation...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(voice_separation_dir)
        zip_path.unlink()
    if str(voice_separation_dir) not in os.environ["PATH"]:
        os.environ["PATH"] = f"{voice_separation_dir};{os.environ['PATH']}"
    logger.info(f"Voice separation tools are ready at: {voice_separation_dir}")


def setup_cpu_limit_for_voice_separation(reserve_cores=2):
    """
    Tự động detect số CPU cores,
    Set số threads = cores - reserve_cores.
    Nếu cores - reserve_cores < 2 thì ép thành 2.
    
    Args:
        reserve_cores (int): Số lượng CPU cores muốn giữ lại cho hệ thống.
    """
    total_cores = multiprocessing.cpu_count()

    limited_cores = total_cores - reserve_cores
    if limited_cores < 2:
        limited_cores = 2

    logger.info(f"[setup_cpu_limit] Total CPU cores detected: {total_cores}")
    logger.info(f"[setup_cpu_limit] Reserving {reserve_cores} cores for system.")
    logger.info(f"[setup_cpu_limit] Setting number of threads to: {limited_cores}")

    # Set biến môi trường giới hạn số threads
    os.environ["OMP_NUM_THREADS"] = str(limited_cores)