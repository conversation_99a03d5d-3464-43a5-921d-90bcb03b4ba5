import subprocess
import logging
import os
from internal.api.claims.model import RenderMode
from pkg.folder_util import get_videos_dir, get_render_dir
from typing import Optional
from functools import cache
import random

from pkg.util import generate_hook_for_perty_expression, generate_pretty_expression

class VoiceSeparation:
    def __init__(self, source_id: str, input_path: str, music_path: str, render_mode: RenderMode = None):
        self.source_id = source_id
        self.input_path = input_path
        self.music_path = music_path
        self.voice_separation_path = None
        self.rendered_path = None
        self.render_mode = render_mode

    @property
    @cache
    def logger(self):
        return logging.getLogger(f"{__class__.__module__}.{__class__.__qualname__}")

    async def separate_voice(self) -> tuple[Optional[str], Optional[Exception]]:
        voice_path = None
        try:
            self.logger.info(f"Start voice separation")
            self.logger.info(f"Input path: {self.input_path}")
            self.logger.info(f"Music path: {self.music_path}")
            voice_path = await self._voice_separation_only_audio()
            self.voice_separation_path = await self._merge_audio_video(input_path=self.input_path, 
                                                                  voice_path=voice_path, 
                                                                  output_path=f"{os.path.join(get_render_dir(), self.source_id)}.voice.mp4", 
                                                                  is_mute=True)
            return self.voice_separation_path, None
        except Exception as e:
            self.logger.critical(f"Exception occurred during voice separation: {e}")
            return None, e
        finally:
            if voice_path:
                os.remove(voice_path)

    async def render_music(self, input_path: str, speed: float = 1.0, volume: float = 0.3) -> tuple[Optional[str], Optional[Exception]]:
        try:
            self.logger.info(f"Start render music")
            self.rendered_path = await self._merge_audio_video(input_path=input_path, 
                                             voice_path=self.music_path, 
                                             output_path=f"{os.path.join(get_render_dir(), self.source_id)}.rendered.mp4", 
                                             speed=speed, 
                                             volume=volume)
            return self.rendered_path, None
        except Exception as e:
            self.logger.critical(f"Exception occurred during render music: {e}")
            return None, e
        
    async def speed_up_video(self, input_path: str, speed: float = 1.0) -> tuple[Optional[str], Optional[Exception]]:
        #create folder source_id
        folder_path_parent = os.path.join(get_render_dir(),self.source_id)
        if not os.path.exists(folder_path_parent):
            os.makedirs(folder_path_parent, exist_ok=True)
        
        output_path = f"{os.path.join(get_render_dir(),self.source_id, self.source_id)}.rendered.mp4"
        duration = random.randint(50, 58)
        zoom_factor = 1.4
        cmd = ["ffmpeg", 
                "-y",
                "-i", input_path,
                "-filter_complex", f"[0:v]setpts={1/speed}*PTS,crop=iw*0.9:ih*0.9:(iw-iw*0.9)/2:(ih-ih*0.9)/2,scale='if(gt(iw,ih),min(iw*1.2\,1920),-1)':'if(gt(ih,iw),min(ih*1.2\,1920),-1)',scale=trunc(iw/2)*2:trunc(ih/2)*2[v];[0:a]atempo={speed}[a]",
                "-map", "[v]",
                "-map", "[a]",
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                "-c:a", "aac",
                "-b:a", "128k",
                "-progress", "pipe:1",
                "-metadata", "title=",
                "-metadata", "artist=",
                "-metadata", "album_artist=",
                "-metadata", "album=",
                "-metadata", "date=",
                "-metadata", "track=",
                "-metadata", "genre=",
                "-metadata", "publisher=",
                "-metadata", "encoded_by=",
                "-metadata", "copyright=",
                "-metadata", "composer=",
                "-metadata", "performer=",
                "-metadata", "TIT1=",
                "-metadata", "TIT3=",
                "-metadata", "disc=",
                "-metadata", "TKEY=",
                "-metadata", "TBPM=",
                "-metadata", "language=",
                "-metadata", "encoder=",
                output_path]
        if self.render_mode and self.render_mode.active:
            # ffmpeg -y -i \"{input_path}\" -filter_complex \"[0:v]scale=1080:-1[vscaled];[vscaled]crop=1080:1080:0:(in_h-out_h)/2[sq];color=black:s=1080x1920:d=1[bg];[bg][sq]overlay=(W-w)/2:(H-h)/2[out];[out]drawtext=text='{header_text}':fontcolor=yellow:fontsize=120:x=(w-text_w)/2:y=(H-1080)/2-140,drawtext=text='{footer_text}':fontcolor=yellow:fontsize=80:x=(w-text_w)/2:y=(H+1080)/2+10,setpts={setpts_value}[v]\" -map \"[v]\" -filter:a \"atempo={atempo_value}\" -metadata title= -metadata artist= -metadata album_artist= -metadata album= -metadata date= -metadata track= -metadata genre= -metadata publisher= -metadata encoded_by= -metadata copyright= -metadata composer= -metadata performer= -metadata TIT1= -metadata TIT3= -metadata disc= -metadata TKEY= -metadata TBPM= -metadata language= -metadata encoder= -c:v libx264 -crf 18 -preset veryfast -c:a aac -b:a 192k \"{output_path}\""
            
            header_text = generate_pretty_expression()
            footer_text = generate_hook_for_perty_expression()
            setpts_value = 1/speed
            atempo_value = speed
            self.logger.info(f"Header text: {header_text}")
            self.logger.info(f"Footer text: {footer_text}")
            
            cmd_str = self.render_mode.ffmpeg
            cmd_str = cmd_str.replace("{input_path}", input_path)
            cmd_str = cmd_str.replace("{output_path}", output_path)
            cmd_str = cmd_str.replace("{header_text}", header_text)
            cmd_str = cmd_str.replace("{footer_text}", footer_text)
            cmd_str = cmd_str.replace("{setpts_value}", str(setpts_value))
            cmd_str = cmd_str.replace("{atempo_value}", str(atempo_value))
            cmd = [cmd_str]
            
        try:
            if self.render_mode and self.render_mode.active:
                process = subprocess.Popen(cmd_str,shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                self.logger.info(line)
            process.stdout.close()
            process.wait()
            if process.returncode != 0:
                self.logger.error(f"Speed up video failed with return code {process.returncode}")
                raise Exception(f"Speed up video failed with return code {process.returncode}")
            else:
                self.logger.info("Speed up video completed successfully.")
                return output_path, None
        except Exception as e:
            self.logger.critical(f"Exception occurred during speed up video: {e}")
            return None, e

    async def _merge_audio_video(self, input_path: str, voice_path: str, output_path: str, speed: float = 1.0, volume: float = 0.3, is_mute: bool = False) -> str:
        cmd = []
        speed = round(speed, 2)
        setpts = 1/speed
        setpts = round(setpts, 2)
        duration = random.randint(50, 58)
        zoom_factor = 1.02
        # -metadata title="" -metadata artist="" -metadata album_artist="" -metadata album="" -metadata date="" -metadata track="" -metadata genre="" -metadata publisher="" -metadata encoded_by="" -metadata copyright="" -metadata composer="" -metadata performer="" -metadata TIT1="" -metadata TIT3="" -metadata disc="" -metadata TKEY="" -metadata TBPM="" -metadata language="eng" -metadata encoder=""
        if is_mute:
            cmd = ["ffmpeg", 
                "-i", input_path,
                "-i", voice_path,
                "-filter_complex", 
                f"[0:v]setpts={setpts}*PTS,scale=iw*{zoom_factor}:ih*{zoom_factor},crop=iw:ih[v];"  # Adjust video speed
                f"[1:a]atempo={speed}[a1]",       # Adjust audio speed
                "-map", "[v]",                    # Use speed-adjusted video
                "-map", "[a1]",                   # Use speed-adjusted audio
                "-c:v", "h264",                   # Use h264 codec
                "-preset", "medium",              # Encoding speed/compression ratio
                "-crf", "23",                     # Quality (18-28 is good, lower is better)
                "-c:a", "aac",
                "-t", str(duration),              # Limit duration
                "-shortest",                      # End when shortest stream ends
                "-metadata", "title=",
                "-metadata", "artist=",
                "-metadata", "album_artist=",
                "-metadata", "album=",
                "-metadata", "date=",
                "-metadata", "track=",
                "-metadata", "genre=",
                "-metadata", "publisher=",
                "-metadata", "encoded_by=",
                "-metadata", "copyright=",
                "-metadata", "composer=",
                "-metadata", "performer=",
                "-metadata", "TIT1=",
                "-metadata", "TIT3=",
                "-metadata", "disc=",
                "-metadata", "TKEY=",
                "-metadata", "TBPM=",
                "-metadata", "language=",
                "-metadata", "encoder=",
                output_path]
        else:
            cmd = ["ffmpeg", 
                "-i", input_path,
                "-i", voice_path,
                "-filter_complex", 
                f"[0:v]setpts={setpts}*PTS,scale=iw*{zoom_factor}:ih*{zoom_factor},crop=iw:ih[v];"  # Adjust video speed
                f"[0:a]atempo={speed}[a0];"       # Adjust original audio speed
                f"[1:a]volume={volume}[a1];"      # Only adjust volume of voice audio
                f"[a0][a1]amix=inputs=2:duration=first[aout]",  # Mix both audio streams
                "-map", "[v]",                    # Use speed-adjusted video
                "-map", "[aout]",                 # Use mixed audio
                "-c:v", "h264",                   # Use h264 codec
                "-preset", "medium",              # Encoding speed/compression ratio
                "-crf", "23",                     # Quality (18-28 is good, lower is better)
                "-c:a", "aac",
                "-t", str(duration),              # Limit duration
                "-shortest",                      # End when shortest stream ends
                "-metadata", "title=",
                "-metadata", "artist=",
                "-metadata", "album_artist=",
                "-metadata", "album=",
                "-metadata", "date=",
                "-metadata", "track=",
                "-metadata", "genre=",
                "-metadata", "publisher=",
                "-metadata", "encoded_by=",
                "-metadata", "copyright=",
                "-metadata", "composer=",
                "-metadata", "performer=",
                "-metadata", "TIT1=",
                "-metadata", "TIT3=",
                "-metadata", "disc=",
                "-metadata", "TKEY=",
                "-metadata", "TBPM=",
                "-metadata", "language=",
                "-metadata", "encoder=",
                output_path]
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                self.logger.info(line)
            process.stdout.close()
            process.wait()
            
            if process.returncode != 0:
                self.logger.error(f"Merge audio video failed with return code {process.returncode}")
                raise Exception(f"Merge audio video failed with return code {process.returncode}")
            else:
                self.logger.info("Merge audio video completed successfully.")
                return output_path
        except Exception as e:
            self.logger.critical(f"Exception occurred during merge audio video: {e}")
            raise e

    async def _voice_separation_only_audio(self) -> str:
        file_name = os.path.basename(self.input_path)
        file_name = f"{os.path.splitext(file_name)[0]}.voice"
        output_dir = get_videos_dir()
        output_path = os.path.join(output_dir, f"{file_name}.mp3")
        cmd = ["voice-separation", 
                "--file-path-input", self.input_path, 
                "--name-file-output", file_name, 
                "--dir-output", output_dir]
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                self.logger.info(line)
            process.stdout.close()
            process.wait()
            if process.returncode != 0:
                self.logger.error(f"Voice separation failed with return code {process.returncode}")
                raise Exception(f"Voice separation failed with return code {process.returncode}")
            else:
                self.logger.info("Voice separation completed successfully.")
                return output_path
        except Exception as e:
            self.logger.critical(f"Exception occurred during voice separation: {e}")
            raise e

