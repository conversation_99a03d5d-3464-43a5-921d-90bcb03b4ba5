
import os
from pathlib import Path
import requests
import logging

logger = logging.getLogger(__name__)

async def setup_ytdlp():
    ytdlp_dir = Path.home() / "yt-dlp"
    ytdlp_dir.mkdir(exist_ok=True)
    ytdlp_exe = ytdlp_dir / "yt-dlp.exe"
    if not ytdlp_exe.exists():
        ytdlp_url = "https://github.com/yt-dlp/yt-dlp/releases/download/2025.03.31/yt-dlp.exe"
        logger.info("Downloading yt-dlp...")
        response = requests.get(ytdlp_url)
        ytdlp_path = ytdlp_dir / "yt-dlp.exe"
        ytdlp_path.write_bytes(response.content)
    # Thêm đường dẫn vào PATH
    if str(ytdlp_dir) not in os.environ["PATH"]:
        os.environ["PATH"] = f"{ytdlp_dir};{os.environ['PATH']}"
    logger.info(f"yt-dlp are ready at: {ytdlp_dir}")