import asyncio
from itertools import cycle
from ffmpegc.setup import setup_ffmpeg
from internal.init.folder import init_folders
from internal.init.ldplayer import init_ldplayer
from internal.ytb.exception.error import ProxyError
from internal.ytb.task import UploadClaimTask
from ldplayer.model import LDPlayerInstance
from ldplayer.setup import setup_ldconsole
import logging
from globals import globals

from voice_separation.setup import setup_voice_separation
from ytdlp.setup import setup_ytdlp
logger = logging.getLogger(__name__)


async def worker(task: LDPlayerInstance):
    logger.info(f"Start task {task=}")
    upload_claim_task = UploadClaimTask(task)
    await asyncio.sleep(2)  # gi<PERSON> lập công việc tốn thời gian
    try:
        result = await upload_claim_task.run()
        if result.is_err():
            raise result.error
    except Exception as e:
        if isinstance(e, ProxyError):
            logger.error(f"Proxy error: {e}")
            await asyncio.sleep(60*60*6)
        else:
            logger.error(f"Error run task: {e}")
    logger.info(f"End task {task=}")
    return "Done"


async def run_tasks_concurrently_forever(tasks: list[LDPlayerInstance], max_concurrent=3):
    task_cycle = cycle(tasks)
    semaphore = asyncio.Semaphore(max_concurrent)

    async def sem_task(task: LDPlayerInstance):
        async with semaphore:
            await worker(task)

    while True:
        # Lấy max_concurrent task và chạy song song
        batch = [asyncio.create_task(sem_task(next(task_cycle))) for _ in range(max_concurrent)]
        await asyncio.gather(*batch)

async def run():
    
    # init folder
    logger.info("Setting up folders")
    result = init_folders()
    if result.is_err():
        raise result.error
    logger.info("Folders are ready")
    
    # setup ld console
    logger.info("Setting up LD console")
    result = setup_ldconsole()
    if result.is_err():
        raise result.error
    logger.info("LD console is ready")
    
    # setup ffmpeg
    logger.info("Setting up FFmpeg")
    await setup_ffmpeg()
    logger.info("FFmpeg is ready")
    
    # setup voice separation
    logger.info("Setting up voice separation")
    await setup_voice_separation()
    logger.info("Voice separation is ready")
    
    # Setup itdlp
    logger.info("Setting up yt-dlp")
    await setup_ytdlp()
    logger.info("yt-dlp is ready")
    
    # Read ld player install
    ldplayer_instance_list = init_ldplayer()
    globals.ldplayer_instance_list = ldplayer_instance_list
    logger.info("LDPlayer is ready")    
    logger.info("All setup are ready")
    
    
    NUMBER_THREAD = 1
    if len(ldplayer_instance_list) < NUMBER_THREAD:
        NUMBER_THREAD = len(ldplayer_instance_list)
    
        
    # Start number thread with task ld
    await run_tasks_concurrently_forever(ldplayer_instance_list, max_concurrent=NUMBER_THREAD)
    