

import asyncio
import os
import random
import re
import subprocess
from time import sleep
from typing import Optional
from google.drive import GoogleService
from google.model import DownloadType
from internal.api.account.serivce import update_log, update_video
from internal.api.agent.service import generate_info_by_video, generate_title_by_deepseek, generate_title_by_gemini, generate_title_by_qwen, generate_title_by_qwen_title
from internal.api.claims.model import LogStatus, PayloadUpdateLog, PayloadUpdateVideo, TaskProfileChildrenState, TaskProfileState, TaskVideo2State, TaskVideoItem, TaskVideoState, VideoStatus
from internal.init import folder
from internal.model.agent import GenerateVideoInfoResponse
from internal.ytb.exception.error import DownloadVideoError, RenderVideoError
from ldplayer.control import connect_ldplayer
from ldplayer.ldconsole import start_ldplayer_by_id
from ldplayer.model import LDPlayerInstance
from ldplayer.setup import setup_file_video_to_config_ld
from pkg.folder_util import delete_folder, get_videos_dir
from pkg.result_model import Result
import logging
from config.setting import settings
import uiautomator2 as u2

from pkg.util import get_speed_settings, get_video_duration, is_english_string
from voice_separation.voice_separation import VoiceSeparation
logger = logging.getLogger(__name__)


class Automation:
    def __init__(self, ldInstance: LDPlayerInstance=None, parent_task: TaskProfileState=None, children_task: TaskProfileChildrenState=None, task_video: TaskVideoState=None):
        self.ldInstance = ldInstance
        self.parent_task = parent_task
        self.children_task = children_task
        self.task_video = task_video
        self.video_render_path = None
        self.video: TaskVideoItem = None
        self.info: GenerateVideoInfoResponse = None
        self.ld_player: u2.Device = None
        
        
    
    async def start(self):
        try:
            # 1. Download video
            result = await self.download_video()
            if result.is_err():
                logger.error(f"Error download video: {result.error}")
                await update_video(PayloadUpdateVideo(videoId=self.task_video.video.id, 
                                                          status=VideoStatus.DOWNLOAD_ERROR))
                await update_log(PayloadUpdateLog(logId=self.task_video.log.id, 
                                                      status=LogStatus.ERROR, 
                                                      reason=str(result.error)))
                raise result.error

            # 2. Get video info
            self.info = None
            logger.info(f"Video description1: {self.video.description}")
            logger.info(f"Video title1: {self.video.name}")
            
            # Random generate title with 10% chance, if children_task.default_title is not None, use children_task.default_title instead
            result = random.choices([True, False], weights=[1, 9])[0]
            #result = True
            if self.children_task.default_title not in [None, '']:
                logger.info(f"Use default title: {self.children_task.default_title}")
                self.info = GenerateVideoInfoResponse(title=self.children_task.default_title, description='')
            elif result:
                logger.info("Random generate title")
                title_random = self.get_title_random()
                title_hashtag_random = title_random + " " +self.get_hashtag_random(title_random)
                self.info = GenerateVideoInfoResponse(title=title_hashtag_random, description='')
            else:
                logger.info("Not random generate title")
                if not self.video.name:
                    logger.warning(f"Not found title1, try generate from video")
                    logger.warning(f"Video path: {self.video_render_path}")
                    self.info = await generate_info_by_video(self.video_render_path)
                else:
                    logger.warning(f"Found tile, try generate title and description")
                    if settings.enable_deepseek:
                        logger.warning("Generate title and description by deepseek")
                        self.info, error = await self.get_video_info("deepseek")
                        if error:
                            logger.error(f"Error get video title and description by deepseek: {error}")
                    if settings.enable_qwen and not self.info:
                        logger.warning("Generate title and description by qwen")
                        self.info, error = await self.get_video_info("qwen")
                        if error:
                            logger.error(f"Error get video title and description by qwen: {error}")
                    if settings.enable_gemini and not self.info:
                        logger.warning("Generate title and description by gemini")
                        self.info, error = await self.get_video_info("gemini")
                        if error:
                            logger.error(f"Error get video title and description by gemini: {error}")
                    if not self.info:
                        raise Exception("Error get video title and description, cannot continue")
            self.info.title = self.format_text(self.info.title, is_title=True)
            self.info.description = self.format_text(self.info.description)
            logger.info(f"Title: {self.info.title}")
            logger.info(f"Description: {self.info.description}")

            # 2. Set video down to ldplayer

            result = setup_file_video_to_config_ld(self.ldInstance.index, self.video_render_path)
            if result.is_err():
                logger.error(f"Error setup file video to config ld: {result.error}")
                raise result.error


            start_result = start_ldplayer_by_id(self.ldInstance.index)
            if start_result.is_err():
                logger.error(f"Error start ldplayer: {start_result.error}")
                raise result.error

            await asyncio.sleep(random.uniform(5, 10))

            connect_result = connect_ldplayer(self.ldInstance)
            if connect_result.is_err():
                logger.error(f"Error connect ldplayer: {connect_result.error}")
                raise result.error
            self.ld_player = connect_result.value


            self.check_and_close_iv()



            logger.info(self.ld_player.info)
            logger.info(f"{self.children_task=}")
            logger.info(f"""Start upload YTB with: 
                        {self.info.title=} 
                        {self.info.description=} 
                        {self.video_render_path=}
                        {self.ldInstance.title=}
                        {self.task_video.music.music_url=}
                        {self.children_task.channel_name}
                        """)
            await update_log(PayloadUpdateLog(logId=self.task_video.log.id, 
                                            status=LogStatus.DONE,
                                            isClaim=True,
                                            youtubeId=None))

            self.upload_autiomation(self.ld_player)
            
        except Exception as e:
            await update_log(PayloadUpdateLog(logId=self.task_video.log.id, 
                                                  status=LogStatus.ERROR, 
                                                  reason=str(e)))
            logger.error(f"Error start automation: {e}")
            raise e

    def check_and_close_iv(self):
        iv_close_el = self.ld_player.xpath('//*[@resource-id="com.android.ld.appstore:id/tv_see_more"]')
        sleep(random.uniform(3, 5))
        iv_close_el.wait(timeout=30.0)

        if iv_close_el.exists:
            logger.info("Tìm thấy nút iv_close")
            self.ld_player.press("back")
        else:
            logger.info("Không tìm thấy nút iv_close")


    async def download_video(self) -> Result[str, Exception]:
        try:
            self.video = self.task_video.video
            logger.info(f"Downloading video id: { self.video.id}")
            logger.info(f"Video: { self.video=}")
            download_url =  self.video.render_url
            if not  self.video.render_url and  self.video.status != VideoStatus.DONE:
                download_url =  self.video.source_url
            logger.info(f"Download url: {download_url}")
            download_path = None

            # download from google drive
            logger.info(f"Download file drive: {download_url}")
            google_service = GoogleService()
            download_path, error = await google_service.download_file(file_id=download_url, source_id= self.video.id, type=DownloadType.VIDEO)
            if error:
                raise DownloadVideoError(str(error))

            if not download_path:
                raise DownloadVideoError("Cannot download video")
            speed = random.uniform(1.02, 1.08)
            duration = get_video_duration(download_path)
            if duration > 58:
                speed = get_speed_settings(duration)
                if speed > 1.2:
                    speed = 1.2
            voice_separation = VoiceSeparation(source_id= self.video.id, input_path=download_path, music_path="", render_mode= self.children_task.render_mode)
            render_path, error = await voice_separation.speed_up_video(input_path=download_path, speed=round(speed, 2))
            if error:
                raise RenderVideoError(str(error))
            self.video_render_path = render_path
            delete_folder(os.path.join(get_videos_dir(),  self.video.id))
            return Result(value=render_path)
        except Exception as e:
            return Result(error=e)
        
    
    async def get_video_info(self, service: str) -> tuple[Optional[GenerateVideoInfoResponse], Optional[Exception]]:
        try:
            title = self.video.name
            description = self.video.description
            logger.warning(f"Found description, try generate title")
            if service == "deepseek":
                title, error = await generate_title_by_deepseek(self.format_text(title.strip()))
                if error:
                    return None, error
                description = ''
            elif service == "qwen":
                title, error = await generate_title_by_qwen_title(self.format_text(title.strip()))
                if error:
                    return None, error
                description = ''
            elif service == "gemini":
                title, error = await generate_title_by_gemini(self.format_text(title.strip()))
                if error:
                    return None, error
                description = ''
            title = self.format_text(title, is_title=True)
            self.info = GenerateVideoInfoResponse(title=title, description='')
            return self.info, None
        except Exception as e:
            return None, e
    
    
    @staticmethod
    def format_text(text: str, is_title: bool = False) -> str:
        """
        Format the text by performing the following steps:
        1. Remove "Replying to @username" if it exists.
        2. Remove URLs.
        3. Remove emoji/icons.
        4. Normalize whitespace (replace multiple spaces with single space).
        5. Normalize underscores (replace multiple underscores with single underscore).
        6. Trim extra spaces from the entire string.
        7. Capitalize the first letter of the string.
        8. if is_title, trim to 90 characters and remove last hashtag if it exists

        Args:
            text (str): The input text string.
            is_title (bool): nếu là tiêu đề thì áp thêm bước cắt và gỡ hashtag cuối.
        Returns:
            str: The formatted text string.
        """
        # Step 1: Remove "Replying to @username"
        text = re.sub(r"Replying to @\S+\s*", "", text)

        # Step 2: Remove URLs
        text = re.sub(r'https?://\S+', '', text)

        # Step 3: Remove emoji/icons (vẫn giữ mọi ký tự Unicode khác)
        emoji_pattern = re.compile(
            "["
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F700-\U0001F77F"  # alchemical symbols
            "\U0001F780-\U0001F7FF"  # Geometric Shapes Extended
            "\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
            "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
            "\U0001FA00-\U0001FA6F"  # Chess Symbols, etc.
            "\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
            "\U00002700-\U000027BF"  # Dingbats
            "\U000024C2-\U0001F251"  # Enclosed characters
            "]+",
            flags=re.UNICODE
        )
        text = emoji_pattern.sub('', text)

        # Step 4: Normalize whitespace (replace multiple spaces with single space)
        text = re.sub(r'\s+', ' ', text)

        # Step 5: Normalize underscores (replace multiple underscores with single underscore)
        text = re.sub(r'_+', '_', text)

        # Step 6: Trim whitespace from the entire string
        text = text.strip()

        # Step 7: Capitalize the first letter of the string
        if text:
            text = text[0].upper() + text[1:]
        
        # Step 8: Nếu là title, giới hạn độ dài và bỏ hashtag cuối (nếu có)
        if is_title:
            # Remove special characters   
            special_chars = "₹&<>✓$¥€©=%}{"
            text = ''.join(char for char in text if char not in special_chars)
            max_length = 95
            is_remove_hashtag = False
            if len(text) > max_length:
                logger.warning(f"Title too long, trim to {max_length} characters")
                is_remove_hashtag = True
                
            text = text[:max_length]
            if is_remove_hashtag:
                    text = re.sub(r"(?:#\S*)?$", "", text).strip()
                    
            

        return text
    
    def upload_autiomation(self, d: u2.Device):
        if d(resourceId="com.android.systemui:id/lock_icon").exists:
            logger.info("Màn hình đang bị khóa")
            d(resourceId="com.android.systemui:id/lock_icon").click()
            sleep(1)
        
            d(resourceId="com.android.systemui:id/key1").click()
            d(resourceId="com.android.systemui:id/key1").click()
            d(resourceId="com.android.systemui:id/key1").click()
            d(resourceId="com.android.systemui:id/key1").click()
            d(resourceId="com.android.systemui:id/key_enter").click()
        
        else:
            logger.info("Màn hình đang mở")
    
        # Kill youtube app
        d.shell(["am", "force-stop", "com.google.android.youtube"])
        count_try = 0
        while True:
            elYTB = d(text="YouTube", className="android.widget.TextView").wait(timeout=5.0)
            if elYTB:
                sleep(1)
                d(text="YouTube", className="android.widget.TextView").click()
                break
            else:
                if count_try > 3:
                    logger.info("Không tìm thấy YouTube")
                    break
                count_try += 1
                d.press("home")
        
        
        # click profile
        retry_count = 2
        while True:
            sleep(random.uniform(3, 5))
            el = d.xpath('//android.widget.TextView[@text="You"]')
            el.wait(timeout=120.0)
            if el.exists:
                logger.info("Tìm thấy nút Profile")
                el.click()
                break
            else:
                retry_count -= 1
                if retry_count == 0:
                    logger.info(f"Not found button Profile parent: { self.parent_task.email} child: {self.children_task.channel_name}")
                    raise Exception(f"Not found button Profile parent: { self.parent_task.email} child: {self.children_task.channel_name}")
                logger.info(f"Not found button Profile, try again {retry_count}")
                self.check_and_close_iv()
        
        
        # Click switch account
        sleep(random.uniform(3, 5))
        el = d.xpath('//*[@content-desc="Switch account"]')
        el.wait(timeout=120.0)
       
        if el.exists:
            logger.info("Tìm thấy nút Switch Account")
            el.click()
        else:
            logger.info("Không tìm thấy nút Switch Account")
            raise Exception("Không tìm thấy nút Switch Account")
        
        google_account_header_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/google_account_header"]')
        google_account_header_el.wait(timeout=180.0)
        sleep(random.uniform(3, 5))
        if google_account_header_el.exists:
            logger.info("Tìm thấy nút Google Account Header ")
        else:
            logger.info(f"Không tìm thấy nút Google Account Header parent: { self.parent_task.email} child: {self.children_task.channel_name}")
            raise Exception(f"Không tìm thấy nút Google Account Header parent: { self.parent_task.email} child: {self.children_task.channel_name}")
        
                    
        d(scrollable=True).scroll.to(text=self.children_task.channel_name)
        sleep(random.uniform(3, 5))
        el = d.xpath(f'//*[@text="{self.children_task.channel_name}"]')
        el.wait(timeout=60.0)
        sleep(random.uniform(3, 5))
        if el.exists:
            logger.info(f"Found channel name {self.children_task.channel_name}")
            el.click()
        else:
            logger.info(f"Not found channel name {self.children_task.channel_name} when switch account")
            raise Exception(f"Not found channel name {self.children_task.channel_name} when switch account")
        
        # Click got it when switch account if show
        got_it_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/bottom_button_row"]')
        got_it_button_el.wait(timeout=60.0)
        sleep(random.uniform(3, 5))
        if got_it_button_el.exists:
            logger.info("Tìm thấy nút Got it")
            got_it_button_el.click()
        else:
            logger.info("Không tìm thấy nút Got it")
            
        
        
        
        # Open music 
        sleep(random.uniform(3, 5))
        url =  self.task_video.music.music_url
        device_id = f"127.0.0.1:{5555 + self.ldInstance.index*2}"
        command = ["adb", "-s", device_id, "shell", "am", "start", "-a", "android.intent.action.VIEW", url]
        #com.google.android.youtube:id/mealbar_dismiss_button
        # Chạy lệnh
        subprocess.run(command)
        
        sleep(random.uniform(3, 5))
        open_with_youtube_el = d.xpath('//android.widget.TextView[@text="YouTube"]')
        open_with_youtube_el.wait(timeout=30.0)
        
        if open_with_youtube_el.exists:
            logger.info("Tìm thấy nút Open with YouTube")
            open_with_youtube_el.click()
            
            always_button_el = d.xpath('//*[@resource-id="android:id/button_always"]')
            always_button_el.wait(timeout=60.0)
            sleep(random.uniform(3, 5))
            if always_button_el.exists:
                logger.info("Tìm thấy nút Always")
                always_button_el.click()
            else:
                logger.info("Không tìm thấy nút Always")
                raise Exception("Không tìm thấy nút Always in Open with YouTube")
            
        else:
            logger.info("Không tìm thấy nút Open with YouTube")
            
        

        for _ in range(random.randint(3, 5)):
            d.swipe(0.5, 0.8, 0.5, 0.3, 0.2)
            sleep(1)  
        
        # check button Use this sound if exist
        use_this_sound_el = d.xpath('//*[@content-desc="Use this sound"]')
        use_this_sound_el.wait(timeout=120.0)
        sleep(random.uniform(3, 5))
        if use_this_sound_el.exists:
            logger.info("Tìm thấy nút Use this sound")
            x1, y1, x2, y2 = use_this_sound_el.get().bounds
   
            y_half = (y1 + y2) // 2
            x_rand = random.randint(x1 + 65, x2 - 65) # sub border 
            y_rand = random.randint(y1, y_half - 30)
            d.click(x_rand, y_rand)
        else:
            logger.info("Không tìm thấy nút Use this sound")
            raise Exception("Không tìm thấy nút Use this sound when open music")
        
        sleep(random.uniform(3, 5))
        el = d.xpath('//*[@resource-id="android:id/button2"]')
        el.wait(timeout=15.0)
        if el.exists:
            logger.info("Tìm thấy nút start over")
            el.click()
        else:
            logger.info("start over not found")
        
        
        
        
        
        sleep(random.uniform(3, 5))
        el = d.xpath('//*[@resource-id="com.google.android.youtube:id/action_bar_root"]')
        el.wait(30)
        if el.exists == False:
            logger.info("Không tìm thấy view action_bar_root")
            raise Exception("Không tìm view nút action_bar_root")
            
        
        sleep(random.uniform(3, 5))
        timeout = 30 # second
        while True:
            sleep(2)
            el = d.xpath('//*[@resource-id="com.google.android.youtube:id/loading_spinner"]')
            if el.exists:
                logger.info("Found processing_indicator_spinner, loading music")
            else:
                break
            if timeout <= 0:
                logger.info("Timeout when loading music")
                raise Exception("Timeout when loading music")
            timeout -= 2
            
        
        
        # Click camera gallery
        sleep(random.uniform(3, 5))
        camera_gallery_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button_delegate"]')
        camera_gallery_el.wait(timeout=30.0)
        if camera_gallery_el.exists:
            logger.info('Tìm thấy nút Camera Gallery')
            camera_gallery_el.click()
        else:
            logger.info('Không tìm thấy nút Camera Gallery')
        
    
        # Click allow permission photo, media, and file
        sleep(random.uniform(3, 5))
        permission_allow_button_el = d.xpath('//*[@resource-id="com.android.packageinstaller:id/permission_allow_button"]')
        permission_allow_button_el.wait(timeout=10.0)
        if permission_allow_button_el.exists:
            logger.info('Tìm thấy nút Allow permission photo, media, and file')
            permission_allow_button_el.click()
        else:
            logger.info('Không tìm thấy nút Allow permission photo, media, and file')
        
        # Click allow permission audio
        sleep(random.uniform(3, 5))
        permission_allow_button_el = d.xpath('//*[@resource-id="com.android.packageinstaller:id/permission_allow_button"]')
        permission_allow_button_el.wait(timeout=10.0)
        if permission_allow_button_el.exists:
            logger.info('Tìm thấy nút Allow permission audio')
            permission_allow_button_el.click()
        else:
            logger.info('Không tìm thấy nút Allow permission audio')
            
    
        # CLick select video in gallery
        select_badge_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_badge"]')
        sleep(random.uniform(3, 5))
        select_badge_el.wait(timeout=30.0)
    
        if select_badge_el.exists:
            logger.info("Found button Select")
            select_badge_el.click()
        else:
            logger.info("Not found button Select")
            raise Exception("Not found button Select")
    
        # Click Next button after select video
        select_next_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_next_button"]')
        sleep(random.uniform(3, 5))
        select_next_button_el.wait(timeout=30.0)
        if select_next_button_el.exists:
            logger.info("Found button Next after select video")
            select_next_button_el.click()
        else:
            logger.info("Not found button Next after select video")
            raise Exception("Not found button Next after select video")
    
        
    
    
        # click done upload 
        click_done_upload_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_trim_finish_trim_button"]')
        sleep(random.uniform(3, 5))
        click_done_upload_el.wait(timeout=30.0)
        if click_done_upload_el.exists:
            logger.info("Found button Done upload")
            click_done_upload_el.click()
        else:
            logger.info("Not found button Done upload")
            raise Exception("Not found button Done upload")
    
        # Wait for upload waiting for indicator not found
        while True:
            sleep(1)    
            el = d(resourceId="com.google.android.youtube:id/processing_indicator_spinner")
            el.wait(timeout=5.0)
            if el.exists:
                logger.info("Found processing_indicator_spinner, đang upload")
            else:
                break
            
        # Click next button after upload
        logger.info("Finish upload")
        shorts_camera_next_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]')
        sleep(random.uniform(3, 5))
        shorts_camera_next_button_el.wait(timeout=5.0)
        if shorts_camera_next_button_el.exists:
            logger.info("Found button shorts_camera_next_button")
            shorts_camera_next_button_el.click()
        else:
            logger.info("Not found button shorts_camera_next_button")
            raise Exception("Not found button shorts_camera_next_button")
        
        
        # Click volume button after upload finish
        el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_edit_volume_button"]')
        sleep(random.uniform(3, 5))
        el.wait(timeout=30.0)
        if el.exists:
            logger.info("Tìm thấy nút Volume")
            el.click()
        else:
            logger.info("Không tìm thấy nút Volume")
            raise Exception("Không tìm thấy nút Volume")
    
    
        volume_track_el = d(resourceId="com.google.android.youtube:id/volume_track")
        sleep(random.uniform(3, 5))
        volume_track_el.wait(timeout=5.0)
        if volume_track_el.exists:
            logger.info("Found volume_track")
            # Check slider exists
            slider = volume_track_el.child(resourceId="com.google.android.youtube:id/slider")
            slider.wait(timeout=5.0)  
            sleep(random.uniform(3, 5))
            # Check slider exists in volume_track_el, if not found, raise exception
            if slider.exists:
                logger.info("Found slider")
                bounds = slider.info['bounds']
                x1 = bounds['left']
                x2 = bounds['right']
                y = (bounds['top'] + bounds['bottom']) // 2  # y cố định giữa thanh
    
                # Swipe from left to right
                d.swipe(x1, y, x2, y, duration=0.3)
                logger.info("Swipe slider to max")
            else:
                logger.info("Not found slider")
                raise Exception("Not found slider")
        else:
            logger.info("Not found volume_track")
            raise Exception("Not found volume_track")
        
        
        shorts_music_volume_control_el = d(resourceId="com.google.android.youtube:id/shorts_music_volume_control")
        sleep(random.uniform(3, 5))
        shorts_music_volume_control_el.wait(timeout=5.0)
        if shorts_music_volume_control_el.exists:
            logger.info("Found shorts_music_volume_control")
            # Found slider in shorts_music_volume_control
            slider = shorts_music_volume_control_el.child(resourceId="com.google.android.youtube:id/slider")
            slider.wait(timeout=5.0)  
            sleep(random.uniform(3, 5))
            # Check slider exists in shorts_music_volume_control, if not found, raise exception
            if slider.exists:
                logger.info("Found slider in shorts_music_volume_control")
                track_bounds = slider.info['bounds']
                track_x1 = track_bounds['left']
                track_x2 = track_bounds['right']
                y = (track_bounds['top'] + track_bounds['bottom']) // 2
    
                total_width = track_x2 - track_x1
                target_x = track_x1 + int(total_width * 0.16)
    
                
                # track_x2 - 9 để tránh chạm vào thanh vuốt
                # Swipe from right to left 20%
                d.swipe(track_x2 - 10, y, target_x, y, duration=0.3)
            else:
                logger.info("Not found slider in shorts_music_volume_control")
                raise Exception("Not found slider in shorts_music_volume_control")
        else:
            logger.info("Not found shorts_music_volume_control")
            raise Exception("Not found shorts_music_volume_control")
        
        # Click done edit volume
        click_done_edit_volume_el = d.xpath('//*[@content-desc="Done"]')
        sleep(random.uniform(3, 5))
        click_done_edit_volume_el.wait(timeout=5.0)
        if click_done_edit_volume_el.exists:
            logger.info("Found button Done")
            click_done_edit_volume_el.click()
        else:
            logger.info("Not found button Done")
            raise Exception("Not found button Done")
        
        
        # Click short post bottom button 
        shorts_post_bottom_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_post_bottom_button"]')
        sleep(random.uniform(3, 5))
        shorts_post_bottom_button_el.wait(timeout=30.0)
        if shorts_post_bottom_button_el.exists:
            logger.info("Found button Post")
            shorts_post_bottom_button_el.click()
        else:
            logger.info("Not found button Post")
            raise Exception("Not found button Post")
        
        
        # Sceen Location
        # Click location
        sleep(random.uniform(3, 5))
        recycler_view_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/recycler_view"]/android.view.ViewGroup/android.view.ViewGroup')
        recycler_view_el.wait(timeout=30.0)
        if recycler_view_el.exists:
            logger.info("Found recycler view")
        else:
            logger.info("Not found recycler view")
            raise Exception("Not found recycler view")
    
        list_view_group = recycler_view_el.all()
        view_group_count = len(list_view_group)
        if view_group_count < 4:
            logger.info("Not enough list_view_group in recycler view")
            raise Exception("Not enough list_view_group in recycler view")
    
        list_view_group[3].click()
    
        # Set location 
        sleep(random.uniform(3, 5))
        search_place_el = d.xpath('//android.widget.EditText[@text="Search place"]')
        search_place_el.wait(timeout=30.0)
        if search_place_el.exists:
            logger.info("Found search place")
            search_place_el.click()
            localtion_text_list = ["USA", "United States"]
            text = random.choice(localtion_text_list)
            d.send_keys(text, clear=True)
    
        else:
            logger.info("Not found search place")
            raise Exception("Not found search place")
    
    
        sleep(random.uniform(3, 5))
        result_search_scroll_view = d.xpath('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup')
        result_search_scroll_view.wait(timeout=30.0)
        if result_search_scroll_view.exists:
            logger.info("Found result search scroll view")
        else:
            logger.info("Not found result search scroll view")
            raise Exception("Not found result search scroll view")
    
        list_view_group = result_search_scroll_view.all()
        view_group_count = len(list_view_group)
        if view_group_count < 6:
            logger.info("Not enough result search scroll view")
            raise Exception("Not enough result search scroll view")
    
        list_view_group[0].click()
    
    
        # Click select audience
        sleep(random.uniform(3, 5))
        recycler_view_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/recycler_view"]/android.view.ViewGroup/android.view.ViewGroup')
        recycler_view_el.wait(timeout=30.0)
        if recycler_view_el.exists:
            logger.info("Found recycler view")
        else:
            logger.info("Not found recycler view")
            raise Exception("Not found recycler view")
    
        list_view_group = recycler_view_el.all()
        view_group_count = len(list_view_group)
        if view_group_count < 5:
            logger.info("Not enough list_view_group in recycler view")
            raise Exception("Not enough list_view_group in recycler view")
    
        list_view_group[4].click()
    
        # Screen child select audience
        sleep(random.uniform(3, 5))
        recycler_view_el = d.xpath('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup')
        recycler_view_el.wait(timeout=30.0)
        if recycler_view_el.exists:
            logger.info("Found recycler view in child select audience")
        else:
            logger.info("Not found recycler view in child select audience")
            raise Exception("Not found recycler view in child select audience")
    
        list_view_group = recycler_view_el.all()
        view_group_count = len(list_view_group)
        if view_group_count < 4:
            logger.info("Not enough list_view_group in recycler view")
            raise Exception("Not enough list_view_group in recycler view")
    
        NOT_MADE_FOR_KIDS_INDEX = 2 
        list_view_group[NOT_MADE_FOR_KIDS_INDEX].click()
    
    
        sleep(random.uniform(3, 5))
        back_button_el = d.xpath('//*[@content-desc="Back"]')
        back_button_el.wait(timeout=30.0)
        if back_button_el.exists:
            logger.info("Found button Back")
            back_button_el.click()
        else:
            logger.info("Not found button Back")
            raise Exception("Not found button Back")
        
        # Input text in Edit video 
        edit_text_el = d.xpath('//android.widget.EditText')
        sleep(random.uniform(3, 5))
        edit_text_el.wait(timeout=60.0)
        if edit_text_el.exists:
            logger.info("Found edit text")
            while True:
                edit_text_el.click()
                sleep(random.uniform(1, 2))
                d.clear_text()
                sleep(random.uniform(1, 2))
                d.send_keys(" ", clear=False)
                sleep(random.uniform(1, 2))
                d.press("del") 
                sleep(random.uniform(1, 2))
                d.send_keys(self.info.title, clear=False)
                sleep(random.uniform(1, 2))
                d.send_keys(" ", clear=False)
                sleep(0.5)
                d.press("del")
                sleep(random.uniform(1, 2))
                if len(edit_text_el.get_text()) > 0:
                    break
                logger.info(f"Clear text again edit: {edit_text_el.get_text()}           title: {self.info.title}")
        else:
            logger.info("Not found edit text")
            raise Exception("Not found edit text")
        
        # Click Upload short button 
        short_upload_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/upload_bottom_button_container"]')
        sleep(random.uniform(3, 5))
        short_upload_button_el.wait(timeout=60.0)
        if short_upload_button_el.exists:
            logger.info("Found button Upload short")
            short_upload_button_el.click()
        else:
            logger.info("Not found button Upload short")
            raise Exception("Not found button Upload short")
        
        
        sleep(random.uniform(3, 5))
        # click profile
        el = d.xpath('//android.widget.TextView[@text="You"]')
        el.wait(timeout=30.0)
        if el.exists:
            logger.info("Tìm thấy nút Profile")
            el.click()
        else:
            logger.info("Không tìm thấy nút Profile")
            raise Exception("Không tìm thấy nút Profile")
        
        # Wait for upload waiting for upload completed
        timeout = 1200 # second
        while timeout > 0:
            sleep(1)
            el = d.xpath('//*[@resource-id="com.google.android.youtube:id/subtitle"]')
            el .wait(timeout=30.0)
            if el.exists:
                logger.info("Found processing_indicator_spinner, đang upload")
                if el.get_text() == "Upload complete":
                    break
            else:
                break
            logger.info(f"Waiting for upload, timeout in {timeout} seconds")
            timeout -= 1
        
        logger.info("Upload completed")
        sleep(random.uniform(14, 20))
        
    def get_title_random(self) -> str:
        title_random_list = [
            "🎬 Guess the movie – no cheating! 👀",
            "Only legends will get this one! 💯🎥",
            "Real fans know this scene 🔥 Can you name it?",
            "Think you know movies? Prove it.👇",
            "🎥 OG movie fans, where you at? Name this!",
            "Bet you’ve seen this. Can you name it though?",
            "If you get this right, you're a real movie nerd 🍿",
            "Only 1% will get this right 😏",
            "Let’s see who the real movie fans are 👇",
            "Movie challenge time! What movie is this?",
            "Let’s play a game: Can you name this movie?",
            "Movie buffs, name this flick! ",
            "Yo, what’s this movie vibe? ",
        ]
        return random.choice(title_random_list)
    
    def get_hashtag_random(self, title: str) -> str:
       hashtag_popular_list = ['#shorts' , '#viralvideo', '#shortvideo', '#fyp','#funny', '#video', '#story', '#viral', '#foryou', '#short',  '#shortsvideo',  '#pov', '#trending']
       hashtag_film_list = ['#movie','#series', '#film','#tv', '#tvshow','#comedy','#show','#fantasy', '#clips','#cine']
       # get random 1 -> 3 hashtag from hashtag_popular_list and hashtag_film_list
       hashtag_random_list = random.sample(hashtag_popular_list, random.randint(2, 3)) + random.sample(hashtag_film_list, random.randint(2, 3))
       
       # shuffle hashtag_random_list
       random.shuffle(hashtag_random_list)
       result = title+ " " + " ".join(hashtag_random_list)
       if len(result) > 98:
           # remove element in list until length of result < 90
           while len(result) > 98 and len(hashtag_random_list) > 0:
               hashtag_random_list.pop()
               result =  title + " " + " ".join(hashtag_random_list) 
       return " ".join(hashtag_random_list) 
