import json
import os
import logging

from pkg.folder_util import read_file
from . import _NAME_LD_CONSOLE, LD_CONSOLE_CMD, PATH_FOLDER_LD_PLAYER

from pkg.cmd_util import run_command_check_result
from pkg.file_util import check_file_exists
from pkg.result_model import Result
logger = logging.getLogger(__name__)





def setup_ldconsole(ldconsole_dir: str = PATH_FOLDER_LD_PLAYER) -> Result[bool, Exception]:
    
    try:
        ldconsole_path = os.path.join(ldconsole_dir, _NAME_LD_CONSOLE)
        if not check_file_exists(ldconsole_path):
            raise FileNotFoundError(f"LD Console not found at: {ldconsole_path}")
        if str(ldconsole_dir) not in os.environ.get("PATH", ""):
            os.environ["PATH"] = f"{ldconsole_dir}{os.pathsep}{os.environ.get('PATH', '')}"
        logger.info(f"LD console tools are ready at: {ldconsole_dir}")
        return run_command_check_result(f"{LD_CONSOLE_CMD}", "ldplayer", is_log=False)
        
    except Exception as e:
        return Result(error=e)

def setup_file_video_to_config_ld(index: int, file_path:str) -> Result[bool, Exception]:
    try:
        path_file_config = os.path.join(PATH_FOLDER_LD_PLAYER, "vms","config", f"leidian{index}.config")
        # Check file exsit
        if not check_file_exists(path_file_config):
            raise FileNotFoundError(f"File config not found at: {path_file_config}")
        # Read file
        with open(path_file_config, "r", encoding="utf-8") as f:
            data = json.load(f)
        # Replace file path
        folder_file = os.path.dirname(file_path)
        data["statusSettings.sharedApplications"] = folder_file
        # Write file
        with open(path_file_config, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        return Result(value=True)
    except Exception as e:
        return Result(error=e)

