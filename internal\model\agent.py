from pydantic import BaseModel, field_validator

class GenerateVideoTitle(BaseModel):
    id: int
    summary: str
    title: str
    keywords: list[str]

class GenerateVideoInfo(BaseModel):
    description: str
    hashtags: list[str]
    title: str
    keywords: list[str]

class GenerateVideoInfoResponse(BaseModel):
    title: str
    description: str

class VideoInfo(BaseModel):
    title: str
    description: str
    hashtags: list[str]
    keywords: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "title": "Sample Video Title",
                "description": "This is a sample video description",
                "hashtags": ["#sample", "#video"],
                "keywords": ["keyword1", "keyword2"]
            }
        }

    @field_validator('title')
    @classmethod
    def validate_title_length(cls, v: str) -> str:
        if len(v) > 100:
            raise ValueError('Title must be less than 100 characters')
        return v

    @field_validator('description')
    @classmethod
    def validate_description_length(cls, v: str) -> str:
        if len(v) > 5000:
            raise ValueError('Description must be less than 5000 characters')
        return v