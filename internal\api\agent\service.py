
from internal.model.agent import GenerateVideoInfoResponse, VideoInfo
from config.config import config
from typing import Optional
import logging
import asyncio
import re
import json
from google import genai
from pkg.fetch_util import fetch

logger = logging.getLogger(__name__)

DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_GEMINI_KEY
}

async def generate_title_by_deepseek(description: str) -> Optional[str]:
    url: str = f"{config.API_GEMINI_URL}/api/generate-text/deepseek"
    payload = {
        "description": description,
        "maxLength": 90,
        "language": "English"
    }

    result, error = await fetch(url, method="POST", json=payload, headers=DEFAULT_HEADERS, timeout=180)
    if result and not result.get('error'):
        return result.get('data', {}).get('title', ''), None
    reason = result.get('message', error)
    return None, Exception(reason)

async def generate_title_by_qwen(description: str) -> Optional[str]:
    url: str = f"{config.API_GEMINI_URL}/api/generate-text/qwen"
    payload = {
        "description": description,
        "maxLength": 90,
        "language": "English"
    }

    result, error = await fetch(url, method="POST", json=payload, headers=DEFAULT_HEADERS, timeout=180)
    if result and not result.get('error'):
        return result.get('data', {}).get('title', ''), None
    reason = result.get('message', error)
    return None, Exception(reason)

async def generate_title_by_qwen_title(description: str) -> Optional[str]:
    url: str = f"{config.API_GEMINI_URL}/api/generate-text/qwen/title"
    payload = {
        "description": description,
        "maxLength": 90,
        "language": "English"
    }

    result, error = await fetch(url, method="POST", json=payload, headers=DEFAULT_HEADERS, timeout=180)
    if result and not result.get('error'):
        return result.get('data', {}).get('title', ''), None
    reason = result.get('message', error)
    return None, Exception(reason)


async def generate_title_by_gemini(description: str) -> Optional[str]:
    url: str = f"{config.API_GEMINI_URL}/api/generate-text"
    payload = {
        "description": description,
        "maxLength": 90,
        "language": "English"
    }

    result, error = await fetch(url, method="POST", json=payload, headers=DEFAULT_HEADERS, timeout=180)
    if result and not result.get('error'):
        return result.get('data', {}).get('title', ''), None
    reason = result.get('message', error)
    return None, Exception(reason)

async def get_api_key_gemini():
    url: str = f"{config.API_GEMINI_URL}/api/conversations/video-summaries/get-apikey"
    result, error = await fetch(url, headers=DEFAULT_HEADERS)
    if result and result.get('apiKey'):
        return result.get('apiKey', ''), None
    reason = result.get('message', error)
    return None, Exception(reason)

async def generate_info_by_video(file_path: str) -> GenerateVideoInfoResponse:
    api_key, error = await get_api_key_gemini()
    if error:
        logger.error(f"Error get api key gemini: {error}")
        raise error
    client = genai.Client(api_key=api_key)
    file = client.files.upload(file=file_path)
    retry = 0
    file_info = client.files.get(name=file.name)
    while retry < config.MAX_RETRY_GET_FILE and file_info.state == "PROCESSING":
        file_info = client.files.get(name=file.name)
        if file_info.state == "ACTIVE":
            break
        elif file_info.state == "FAILED":
            raise Exception("Failed to get file info")
        retry += 1
        logger.info(f"retry get file info: {retry}")
        await asyncio.sleep(5)

    if file_info.state != "ACTIVE":
        raise Exception("Failed to get file info")
    async def _get_info_by_api() -> tuple[VideoInfo, Optional[Exception]]:
        try:
            defaultPrompt = f'''
            Your task is to generate a titles and descriptions used on YouTube Shorts. Your tasks for each title are:
            
            1. Rewrite the video title to:
                Keep the original meaning and story context.

                Make it more emotional, cinematic, and engaging.

                Increase curiosity and click-through rate.

                Use natural language that appeals to a global Shorts audience (under 60 characters if possible).

            2. Select 2–3 optimized hashtags from the list below that best match:
                The topic (movie, comedy, story, tvshow, etc.)

                The format (shorts, shortvideo...)

                The viral potential (#foryou, #fyp, #viral, etc.)

                Avoid overly generic or redundant tags (like #video, #clips)
            
            Be sure to:
            - Provide a clear and concise description of the video content that:
            - Is no longer than *${4000} characters*.
            - Provide a clear and concise title that encapsulates the main topic of the video.
            - Make sure the title is grammatically correct, easy to understand, and no longer than *${90} words.*  
            - Generate up to 5 *hashtags* for the video based on its content, ensuring they are relevant and engaging.
            - Make sure the hashtag begin with "#" character.
            - Ensure the output is structured in valid JSON format for easy usage in web applications.
            - Ensure the response is returned in *English*.
            '''
            result = client.models.generate_content(
                model="models/gemini-2.0-flash", contents=[file_info, defaultPrompt]
            )
            json_str = re.search(r'```json\n(.*?)\n```', result.text, re.DOTALL).group(1)
            json_data = json.loads(json_str)
            return VideoInfo(**json_data), None
        except Exception as e:
            return None, e
    await asyncio.sleep(20)
    retry_get_info = 0
    info, error = await _get_info_by_api()
    if error:
        raise error
    while info is None and retry_get_info < config.MAX_RETRY_GET_FILE:
        retry_get_info += 1
        logger.warning(f"info is None, retry get video info: {retry_get_info}")
        info, error = await _get_info_by_api()
        if error:
            raise error
        await asyncio.sleep(20)
    title = info.title
    description = info.description
    for hashtag in info.hashtags:
        title += f"{hashtag} "
        description += f"{hashtag} "
    if len(info.hashtags) > 0:
        title = title[:-1]
        description = description[:-1]
    return GenerateVideoInfoResponse(title=title, 
                                     description=description)