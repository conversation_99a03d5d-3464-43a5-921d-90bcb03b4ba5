



import asyncio
import random
from internal.api.account.serivce import fetch_profile_account, fetch_profile_with, release_profile
from internal.api.claims.model import ProfileAction, TaskProfileState, TaskVideoState
from internal.api.claims.serivce import fetch_task_profile, fetch_task_profile_children, fetch_task_video, fetch_task_video2
from internal.ytb.exception.error import NotGetTaskChildrenError, ProxyError
from internal.ytb.upload_claim_ld.automation import Automation
from ldplayer.ldconsole import stop_ldplayer_by_id
from ldplayer.model import LDPlayerInstance
import logging

from pkg.result_model import Result
logger = logging.getLogger(__name__)
from internal.proxy.proxy import proxy_manager

class UploadClaimTask:
    def __init__(self, ldInstance: LDPlayerInstance=None):
        self.ldInstance = ldInstance
        self.parent_task: TaskProfileState = None
        self.children_task = None
        self.video: TaskVideoState = None
        self.video_render_path: str = None
        self.automation: Automation = None
        self.account_info = None
        self.proxy = None
        
    
    async def run(self) -> Result[bool, Exception]:
        
        error = None
        
        try:
            result = await self.pre_upload()
            if result.is_err():
                raise result.error
            self.automation = Automation(self.ldInstance, self.parent_task, self.children_task, self.video)
            await self.automation.start()
        except Exception as e:
            error = e
            logger.error(f"Error run task: {e}")
            
        finally:
            try:
                if isinstance(error, NotGetTaskChildrenError):
                    return Result(error=NotGetTaskChildrenError(error.message))
                
                del self.automation
                stop_ldplayer_by_id(self.ldInstance.index)
                logger.info(f"Stop ldplayer {self.ldInstance.index}")
                await release_profile(self.parent_task.id)
                logger.warning("Release profile done")
                logger.warning("Task done")
            except Exception as e:
                logger.error(f"Error stop ldplayer: {e}")
            
            time_sleep = random.uniform(15, 20)
            logger.info(f"Sleep {time_sleep} before next task")
            await asyncio.sleep(time_sleep)
            if error:
                return Result(error=error)
            
            
            
        return Result(value=True)
        
    
    
    async def pre_upload(self) -> Result[bool, Exception]:
        try:
            # fetch task
            logger.info("fech task")
        
            result = await fetch_profile_with(self.ldInstance.title)
           
            if result.is_err():
                logger.error(f"error fetch_profile_with email {self.ldInstance.title}: {result.error}")
                raise Result(error=result.error)
            self.parent_task = result.value
            
            profile_info, account_info, error = await fetch_profile_account(self.parent_task.id)
            if error:
                logger.error(f"error fetch_profile_account: {error}")
                return
            self.proxy = await proxy_manager.get_proxy(profile_info)
            self.account_info = account_info
            self.profile_info = profile_info
            
            # fetch children task
            logger.info("fetch children task")
            
            children_task, error = await fetch_task_profile_children(self.parent_task.id)
            if error:
                logger.error(f"error fetch_task_children: {error}, delay 60s")
                await asyncio.sleep(60)
                raise NotGetTaskChildrenError(error)
            self.children_task = children_task
            
            
            # fetch video
            logger.info("fetch video")
            self.video, error = await fetch_task_video(self.children_task.id)
            return Result(value=True)
        except Exception as e:
            logger.error(f"Error pre upload: {e}")
            return Result(error=e)

            
            
            
    async def upload(self):
        pass
    
    async def postupload(self):
        pass
    