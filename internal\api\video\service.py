
from typing import Optional
import logging
from config.config import config
from internal.api.claims.model import TaskVideo2State
from config.setting import settings
from pkg.fetch_util import fetch
logger = logging.getLogger(__name__)


DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_CLAIM_KEY
}

async def fetch_task_video2(profile_id: str) -> tuple[Optional[TaskVideo2State], Optional[Exception]]:
    url: str = f"{config.API_CLAIM_URL}/api/task/video2"
    params = {
        "profileId": profile_id,
    }
    logger.info(f"fetch_task_video url: {url}")
    logger.info(f"fetch_task_video params: {params}")
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    logger.info(f"fetch_task_video result: {result}")
    if result and not result.get('message'):
        return TaskVideo2State(**result.get('data', {})), None
    reason = result.get('message', error)
    return None, Exception(reason)