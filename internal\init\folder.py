from pkg import folder_util as folder
from pkg.result_model import Result
import logging
logger = logging.getLogger(__name__)

def init_folders() -> Result[bool, Exception]:
    try:
        homeDir = folder.get_home_directory()
        logger.info(f"Home directory: {homeDir}")
        folder.check_or_create_directory(homeDir)
        folder.check_or_create_directory(folder.get_download_dir())
        folder.check_or_create_directory(folder.get_resources_dir())
        folder.check_or_create_directory(folder.get_videos_dir())
        folder.check_or_create_directory(folder.get_music_dir())
        folder.check_or_create_directory(folder.get_render_dir())
        folder.clear_directory(folder.get_videos_dir())
        folder.clear_directory(folder.get_music_dir())
        folder.clear_directory(folder.get_render_dir())
        return Result(value=True)
    except Exception as e:
        return Result(error=e)
