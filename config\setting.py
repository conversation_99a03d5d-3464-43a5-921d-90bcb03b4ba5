from config.config import config
from typing import Optional

class Settings:
    def __init__(self):
        self.task_id: Optional[str] = None
        self.debug: bool = False
        self.version = config.VERSION
        self.timeout_reset_tool: int = 0
        self.use_video2x: bool = False
        self.skip_block_time: bool = False
        self.fix_copyright: bool = False
        self.enable_deepseek: bool = False
        self.enable_qwen: bool = True
        self.enable_gemini: bool = True
        self.enable_claim: bool = False
        self.volume: float = 0.3
        self.enable_parent_profile: bool = False
        self.is_parent: bool = False

settings = Settings()
