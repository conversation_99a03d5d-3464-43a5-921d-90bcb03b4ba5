

from time import sleep, time
import uiautomator2 as u2
import subprocess

from ldplayer.model import LDPlayerInstance
from pkg.result_model import Result


def find_element(d: u2.Device, xpath: str):
    el = d.xpath(xpath)
    if el.exists:
        return el
    else:
        return None
    
def find_element_by_text(d: u2.<PERSON>ce, text: str):
    el = d(text=text)
    if el.exists:
        return el
    else:
        return None

def find_element_by_resourceId(d: u2.<PERSON><PERSON>, resourceId: str ):
    el = d(resourceId=resourceId)
    if el.exists:
        return el
    else:
        return None

def find_element_with_timeout(d: u2.Device, xpath: str, timeout: int):
    el = d.xpath(xpath).wait(timeout=timeout)
    if el.exists:
        return el
    else:
        return None
    
def find_element_by_text_with_timeout(d: u2.Devi<PERSON>, text: str, timeout: int):
    el = d(text=text).wait(timeout=timeout)
    if el.exists:
        return el
    else:
        return None

def find_element_by_resourceId_with_timeout(d: u2.<PERSON><PERSON>, resourceId: str, timeout: int):
    el = d(resourceId=resourceId).wait(timeout=timeout)
    if el.exists:
        return el
    else:
        return None

def adb_shell_on(serial, command):
    subprocess.run(["adb", "-s", serial, "shell"] + command.split(), check=True)

# Sử dụng
# device_id = "127.0.0.1:5555"  # hoặc thiết bị bạn chọn từ adb devices
# adb_shell_on(device_id, "rm -rf /sdcard/DCIM/Camera/*")


def connect_ldplayer(ldplayer: LDPlayerInstance) -> Result[u2.Device, Exception]:
    max_retries = 10 
    delay_between_retries = 15 # second 
    
    for attempt in range(1, max_retries + 1):
        try:
            device_id = f"127.0.0.1:{5555 + ldplayer.index*2}"
            d = u2.connect(device_id)
            return Result(value=d)
        except Exception as e:
            if attempt < max_retries:
                sleep(delay_between_retries)
            else:
                return Result(error=e)
            
def open_url_in_browser(d: u2.Device, url: str):
    command = ["am", "start", "-a", "android.intent.action.VIEW", url]
    d.shell(command)
    