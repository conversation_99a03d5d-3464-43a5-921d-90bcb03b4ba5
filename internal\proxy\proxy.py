
from functools import cache
import logging
from pydantic import BaseModel
import asyncio

from internal.model.profile import ProfileState
from internal.model.proxy import CheckProxyResponse, ProxyInfo
from config.config import config
from internal.proxy.serivce import check_proxy, fetch_proxy
from config.config import config
from internal.ytb.exception.error import ProxyError
logger = logging.getLogger(__name__)
class Proxy(BaseModel):
    info: ProxyInfo
    check: CheckProxyResponse
    current_ip: str
    country: str

class ProxyManager:

    async def get_proxy(self, profile: ProfileState) -> Proxy:
        for i in range(config.MAX_RETRY_GET_PROXY):
            proxy, err = await fetch_proxy(profile.id)
            if err is not None:
                raise Exception(f"Error fetching proxy profile {profile.email}: {err}")
            # check proxy
            proxy_info = ProxyInfo(protocol=proxy.protocol, 
                                   address=proxy.address, 
                                   username=proxy.username, 
                                   password=proxy.password)
            check_proxy_info, err = await check_proxy(proxy_info)
            if err is not None:
                logger.error(f"Error checking proxy: {err}, retry {i + 1}")
                await asyncio.sleep(config.DELAY_GET_PROXY)
                continue
            return Proxy(info=proxy_info, 
                         check=check_proxy_info, 
                         current_ip=check_proxy_info.query, 
                         country=check_proxy_info.country)
        raise ProxyError(f"Error getting proxy {proxy_info.address}, max retry")

proxy_manager = ProxyManager()
