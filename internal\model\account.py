from pydantic import BaseModel, Field
from typing import Optional
from enum import Enum

class AccountStatus(str, Enum):
    PENDING = "pending"
    LIVE = "live"
    DIE = "die"
    ERROR = "error"
    BLOCKED = "blocked"
    VERIFY = "verify"
    DISABLED = "disabled"
    CHANNEL_DIE = "channelDie"
    def __str__(self):
        return self.value

class AccountState(BaseModel):
    id: str
    email: str
    password: str
    recovery: Optional[str] = Field(default=None)
    two_factor: Optional[str] = Field(alias="twoFactor", default=None)
    phone: Optional[str] = Field(default=None)
    country: Optional[str] = Field(default=None)
    backup_codes: list[str] = Field(alias="backupCodes")
    status: AccountStatus
    reason: Optional[str] = Field(default=None)
    is_change_info: Optional[bool] = Field(alias="isChangeInfo", default=None)
    note: Optional[str] = Field(default=None)
    old_info: Optional[dict] = Field(alias="oldInfo", default=None)
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    group_id: str = Field(alias="groupId")
    profile_id: Optional[str] = Field(alias="profileId", default=None)
    platform_id: str = Field(alias="platformId")

class UpdateAccountPayload(BaseModel):
    accountId: str
    profileId: Optional[str] = Field(default=None)
    status: AccountStatus
    reason: Optional[str] = Field(default=None)

class SaveAccountPayload(BaseModel):
    profile: str
    email: str
    password: str
    recoverMail: Optional[str] = Field(default=None)
    twoFactor: Optional[str] = Field(default=None)
    country: Optional[str] = Field(default=None)
    groupId: Optional[str] = Field(default=None)
    saveGroupId: Optional[str] = Field(default=None)

class SkipAccountPayload(BaseModel):
    profile: str
    country: Optional[str] = Field(default=None)
    skipGroupId: Optional[str] = Field(default=None)
