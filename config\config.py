from functools import lru_cache
from typing import Optional

from pydantic_settings import BaseSettings, SettingsConfigDict


class BaseConfig(BaseSettings):
    ENV_STATE: Optional[str] = "prod"

    model_config = SettingsConfigDict(env_file=None, extra="ignore")


class GlobalConfig(BaseConfig):
    NAME_APP: Optional[str] = "LD claims"
    NAME_APP_FOLDER: str = "claimsld"
    
    API_URL: str = "https://api.goprofilev4.net"
    API_KEY: str = "dUnNTNtMoeBQNZvdsP4Nc4q8AP8JXmvM"
    WORKER_URL: str = "https://worker.goprofilev2.net/ip"
    APP_NAME: str = "pyprofile"
    VERSION: str = "0.0.1"
    # Claim
    API_CLAIM_URL: str = "https://api-claim.govideovn.store"
    API_CLAIM_KEY: str = "dUnNTNtMoeBQNZvdsP4Nc4q8AP8JXmvM"
    # Gemini
    API_GEMINI_URL: str = "https://api-summary.atechdigital.space"
    API_GEMINI_KEY: str = "01JJ3TYDK1QDJ5BVF9N73M49N0"
    # Time
    BLOCK_TIME_FROM: int = 20
    BLOCK_TIME_TO: int = 22
    # Setting
    MAX_RETRY_GET_FILE: int = 30
    MAX_RETRY_CREATE_CHANNEL: int = 3
    TIMEOUT_UPLOAD: int = 30 * 60 # 30 minutes
    MAX_RETRY_LOGIN: int = 3
    MAX_TRY_GENERATE_TITLE: int = 3
    MAX_TRY_NEXT_STEP: int = 10
    MAX_TRY_RESTORE_PAGE: int = 3
    # Proxy
    DELAY_GET_PROXY: int = 5
    MAX_RETRY_GET_PROXY: int = 3
    


class DevConfig(GlobalConfig):
    pass
    #model_config = SettingsConfigDict(env_prefix="DEV_")


class ProdConfig(GlobalConfig):
    pass
    #model_config = SettingsConfigDict(env_prefix="PROD_")


@lru_cache()
def get_config(env_state: str):
    configs = {"dev": DevConfig, "prod": ProdConfig}
    return configs[env_state]()


config = get_config(BaseConfig().ENV_STATE)
