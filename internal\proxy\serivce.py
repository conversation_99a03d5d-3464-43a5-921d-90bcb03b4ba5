


from typing import Optional
from internal.model.proxy import ProxyState
from config.config import config
from internal.model.proxy import ProxyInfo, CheckProxyResponse
from pkg.fetch_util import fetch

import logging
logger = logging.getLogger(__name__)


DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_KEY
}

async def fetch_proxy(profile_id: str) -> tuple[Optional[ProxyState], Optional[Exception]]:
    try:
        url: str = f"{config.API_URL}/api/proxy/auto"
        params = {
            "profileId": profile_id,
            "action": "upload"
        }
        logger.info(f"fetch_proxy params: {params}")
        result, error = await fetch(url, params=params, headers=DEFAULT_HEADERS)
        if result and result.get('success'):
            return ProxyState(**result.get('data', {})), None
        reason = result.get('message', error)
        return None, Exception(reason)
    except Exception as e:
        logger.critical(f"catch error fetch_proxy: {e}")
        return None, e
    
async def check_proxy(payload: ProxyInfo) -> tuple[Optional[CheckProxyResponse], Optional[Exception]]:
    try:
        proxy_url: str = f"{payload.address}"
        if payload.username and payload.password and len(payload.username) > 0 and len(payload.password) > 0:
            proxy_url = f"{payload.username}:{payload.password}@{proxy_url}"
        proxy_url = f"{payload.protocol.value}://{proxy_url}"
        logger.info(f"check_proxy proxy_url: {proxy_url}")
        result, error = await fetch(config.WORKER_URL, proxy=proxy_url, timeout=30)
        if result and result.get('status') == 'success':
            return CheckProxyResponse(**result), None
        reason = error if error else "Proxy is not working"
        return None, Exception(reason)
    except Exception as e:
        logger.critical(f"catch error check_proxy: {e}")
        return None, e