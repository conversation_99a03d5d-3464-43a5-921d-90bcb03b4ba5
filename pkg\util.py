import re

import getpass

import random

from moviepy import VideoFileClip



def is_email(title: str) -> bool:
    return re.match(r"[^@]+@[^@]+\.[^@]+", title) is not None

def name_user_windows() -> str:
    return f"{getpass.getuser()}"


def get_source_id(url: str) -> str:
    # https://www.youtube.com/watch?v=DQODluUlPG0 ==> DQODluUlPG0
    # https://www.youtube.com/shorts/mWD-lBFLD58 ==> mWD-lBFLD58
    # https://www.youtube.com/channel/UCzybe_mMxv848rF6lrrNI2g ==> UCzybe_mMxv848rF6lrrNI2g
    # https://www.youtube.com/source/r5eeq2DCn-k/shorts ==> r5eeq2DCn-k
    # https://drive.google.com/drive/u/0/folders/1ML_K4arNWKaJat_gu-_ZD-j-xH0ceKe8 ==> 1ML_K4arNWKaJat_gu-_ZD-j-xH0ceKe8
    if "youtube.com" in url:
        if "/channel/" in url:
            return url.split("channel/")[1].split("?")[0]
        elif "/watch" in url:
            return url.split("v=")[1].split("&")[0]
        elif "/source" in url:
            return url.split("shorts/")[1].split("?")[0]
    elif "drive.google.com" in url:
        if "/folders/" in url:
            return url.split("folders/")[1].split("?")[0].split("/")[0]
        elif "/file/d/" in url:
            return url.split("file/d/")[1].split("?")[0].split("/")[0]
    return url


def is_english_string(s: str, threshold: float = 0.9) -> bool:
    """
    Check if a string is primarily English text.
    Args:
        s: The string to check
        threshold: The minimum percentage of English characters required (default: 0.9 or 90%)
    Returns:
        True if the string is primarily English, False otherwise
    """
    if not s:
        return True
        
    english_chars = 0
    total_chars = 0
    
    for char in s:
        # Skip whitespace and common punctuation
        if char.isspace() or char in '.,!?;:\'"()[]{}':
            continue
            
        total_chars += 1
        try:
            char.encode('ascii')
            english_chars += 1
        except UnicodeEncodeError:
            pass
            
    if total_chars == 0:
        return True
        
    return (english_chars / total_chars) >= threshold



def generate_pretty_expression():
    ops = ['+', '-', '*']

    while True:
        numbers = [random.randint(1, 10) for _ in range(5)]

        # Chọn phép toán đảm bảo đủ 3 loại
        while True:
            chosen_ops = [random.choice(ops) for _ in range(4)]
            if len(set(chosen_ops)) >= 3:
                break

        expr_parts = [str(numbers[0])]
        current_value = numbers[0]
        valid = True

        for i in range(4):
            op = chosen_ops[i]
            num = numbers[i + 1]

            if op == '/':
                # Đảm bảo chia hết
                possible_divisors = [d for d in range(1, 11) if current_value % d == 0]
                if not possible_divisors:
                    valid = False
                    break
                num = random.choice(possible_divisors)

            expr_parts.append(op)
            expr_parts.append(str(num))

            # Cập nhật kết quả tạm để phục vụ chia
            if op == '+':
                current_value += num
            elif op == '-':
                current_value -= num
            elif op == '*':
                current_value *= num
            elif op == '/':
                current_value //= num

        if valid:
            # Thay thế ký hiệu hiển thị đẹp hơn
            display_expr = ''.join(expr_parts)
            display_expr = display_expr.replace('*', '×').replace('/', '÷')
            return display_expr + "=?"

def generate_hook_for_perty_expression():

    hooks = [
        "Thought it was easy? Think again",
        "Has anyone figured it out yet?",
        "Which number did you pick?",
        "What did you think at first?",
        "Ready for the big reveal?",
        "Get it wrong and it is auto-unfollow",
        "Only the smart ones get a follow",
        "Only the smart ones will get it",
        "5 seconds, no calculator!",
        "Can you solve it fast?",
        "Most people get this wrong",
        "Only 1 in 100 can solve this",
        "Looks simple... until you try it",
        "Prove you are not average",
        "IQ 130+ only. Let is see ",
        "Bet you will overthink this one",
        "The ending will mess with your brain",
        "You will not believe the answer ",
        "This one is trickier than it looks",
        "Still think you are smart? Try this",
        "Everyone argues about this one",
        "Solve it before the timer runs out",
        "No one gets it on the first try",
    ]

    # Randomly select a hook
    random_hook = random.choice(hooks)
    return random_hook

def get_video_duration(filepath):
    clip = VideoFileClip(filepath)
    return clip.duration

def get_speed_settings(original_duration, target_range=(52, 58)) -> float:
    target_duration = random.uniform(*target_range)
    speed_factor = original_duration / target_duration
    return speed_factor