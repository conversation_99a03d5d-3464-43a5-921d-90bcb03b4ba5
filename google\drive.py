from typing import Optional
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

from internal.model.google import CredentialsState, TokensState
from pkg.folder_util import get_music_dir, get_videos_dir
from pkg.util import get_source_id

from .model import FileInfoState, ListFilesState, UploadFileState, SharePublicState, DownloadType
from .api import fetch_google, fetch_teamdrive

import os
import logging
from functools import cache

SCOPES = ['https://www.googleapis.com/auth/drive']

class GoogleService:
    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = GoogleService()
        return cls._instance

    def _authorize(self, credentials: CredentialsState, tokens: TokensState) -> Credentials:
        client_id = credentials.installed.client_id
        client_secret = credentials.installed.client_secret
        redirect_uris = credentials.installed.redirect_uris
        creds = Credentials(
            token=tokens.access_token,
            refresh_token=tokens.refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=client_id,
            client_secret=client_secret
        )
        return creds
    
    @property
    @cache
    def logger(self):
        return logging.getLogger(f"{__class__.__module__}.{__class__.__qualname__}")

    async def get_info_file(self, file_id: str, show_all: bool = False) -> tuple[Optional[FileInfoState], Optional[Exception]]:
        try:
            account, error = await fetch_google()
            if error:
                return None, error
            auth = self._authorize(account.credentials, account.tokens)
            drive = build('drive', 'v3', credentials=auth)
            fields = "*" if show_all else 'size,mimeType,videoMediaMetadata,resourceKey,thumbnailLink,name,modifiedTime,createdTime'
            result = drive.files().get(fileId=file_id, fields=fields, supportsAllDrives=True).execute()
            return FileInfoState(**result), None
        except Exception as e:
            return None, e

    async def get_list_files(self, folder_id: str) -> tuple[Optional[ListFilesState], Optional[Exception]]:
        try:
            account, error = await fetch_google()
            if error:
                return None, error
            auth = self._authorize(account.credentials, account.tokens)
            drive = build('drive', 'v3', credentials=auth)
            q = f"'{folder_id}' in parents"
            fields = 'nextPageToken, files(id, name, mimeType, size, modifiedTime, createdTime)'
            result = drive.files().list(q=q, fields=fields, supportsAllDrives=True).execute()
            return ListFilesState(**result), None
        except Exception as e:
            return None, e

    async def share_public(self, file_id: str) -> tuple[Optional[SharePublicState], Optional[Exception]]:
        try:
            account, error = await fetch_google()
            if error:
                return None, error
            auth = self._authorize(account.credentials, account.tokens)
            drive = build('drive', 'v3', credentials=auth)
            body = {"role": "reader", "type": "anyone"}
            result = drive.permissions().create(fileId=file_id, body=body, supportsAllDrives=True).execute()
            return SharePublicState(**result), None
        except Exception as e:
            return None, e

    async def upload_file(self, file_name: str, file_path: str) -> tuple[Optional[UploadFileState], Optional[Exception]]:
        try:
            account, error = await fetch_google()
            if error:
                return None, error
            teamdrive, error = await fetch_teamdrive()
            if error:
                return None, error
            auth = self._authorize(account.credentials, account.tokens)
            drive = build('drive', 'v3', credentials=auth)
            file_metadata = {"name": file_name}
            file_metadata["parents"] = [teamdrive.folder_id]
            media = MediaFileUpload(file_path, resumable=True)
            result = drive.files().create(
                body=file_metadata,
                media_body=media,
                supportsAllDrives=True
            ).execute()
            return UploadFileState(**result), None
        except Exception as e:
            return None, e
    
    async def download_file(self, file_id: str, source_id: str, type: DownloadType = DownloadType.VIDEO) -> tuple[Optional[str], Optional[Exception]]:
        try:
            file_id = get_source_id(file_id)
            account, error = await fetch_google()
            if error:
                return None, error
            output_path = os.path.join(get_videos_dir(), f"{source_id}.download.mp4")
            # check if file already exists
            if os.path.exists(output_path):
                self.logger.info(f"File already exists: {source_id}")
                return output_path, None
            
            # create folder source_id
            # folder_path = os.path.join(get_videos_dir(), f"{source_id}")
            # if not os.path.exists(folder_path):
            #     os.makedirs(folder_path, exist_ok=True)
            
            auth = self._authorize(account.credentials, account.tokens)
            drive = build('drive', 'v3', credentials=auth)
            if type == DownloadType.MUSIC:
                output_path = os.path.join(get_music_dir(), f"{source_id}.download.mp4")
            result = drive.files().get_media(fileId=file_id, supportsAllDrives=True).execute()
            with open(output_path, 'wb') as f:
                f.write(result)
            return output_path, None
        except Exception as e:
            return None, e

google_service = GoogleService.get_instance()
