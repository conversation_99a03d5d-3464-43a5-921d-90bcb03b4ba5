import subprocess
import re
import logging

from pydantic import BaseModel
from pkg.folder_util import get_videos_dir, get_music_dir
from typing import Optional

from pkg.result_model import Result
from .model import DownloadType
import os
import glob
logger = logging.getLogger(__name__)

async def download_file_youtube(url: str, source_id: str, type: DownloadType = DownloadType.VIDEO) -> tuple[Optional[str], Optional[Exception]]:
    logger.info(f"Downloading {type.value} from {url}")
    output_dir = get_videos_dir()
    if type == DownloadType.MUSIC:
        output_dir = get_music_dir()
    
    # Create a temporary directory for download
    temp_dir = os.path.join(output_dir, f"{source_id}")

    # check if file already exists
    if os.path.exists(temp_dir):
        logger.info(f"File already exists: {source_id}")
        # Find the downloaded file in temp directory
        downloaded_files = glob.glob(os.path.join(temp_dir, f"{source_id}.download.*"))
        if downloaded_files:
            actual_file_path = downloaded_files[0]
            logger.info(f"Download completed successfully. File saved at: {actual_file_path}")
            return actual_file_path, None
    else:
        os.makedirs(temp_dir, exist_ok=True)
    
    # Set output path in temp directory
    output_path = os.path.join(temp_dir, f"{source_id}.download.%(ext)s")
    cmd = ['yt-dlp', '-o', output_path, url]
    # proxy, error = await fetch_proxy()
    # if error is None:
    #     cmd.extend(["--proxy", f"http://{proxy.username}:{proxy.password}@{proxy.proxy}"])
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        # Read output line by line
        while True:
            line = process.stdout.readline()
            if not line:
                break
            line = line.strip()
            match = re.search(r"(\d+(?:\.\d+)?)%", line)
            if match:
                progress = match.group(1)
                logger.debug(f"Download progress: {progress}%")
            else:
                logger.debug(line)
        process.stdout.close()
        process.wait()
        
        if process.returncode != 0:
            logger.error(f"Download failed with return code {process.returncode}")
            return None, Exception(f"Download failed with return code {process.returncode}")
        
        # Find the downloaded file in temp directory
        downloaded_files = glob.glob(os.path.join(temp_dir, f"{source_id}.download.*"))
        if not downloaded_files:
            logger.error("No file was downloaded")
            return None, Exception("No file was downloaded")
        
        # Get the actual downloaded file path
        actual_file_path = downloaded_files[0]
        logger.info(f"Download completed successfully. File saved at: {actual_file_path}")
        
        return actual_file_path, None
        
    except Exception as e:
        logger.critical(f"Exception occurred during download: {e}")
        return None, e
