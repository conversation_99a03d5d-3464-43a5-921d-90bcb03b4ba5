from pydantic import BaseModel, Field
from enum import Enum
from typing import Optional


class ProxyProtocol(Enum):
    HTTP = "http"
    HTTPS = "https"
    SOCKS5 = "socks5"

class ProxyInfo(BaseModel):
    protocol: ProxyProtocol
    address: str
    username: Optional[str] = Field(default="")
    password: Optional[str] = Field(default="")

class ProxyState(BaseModel):
    id: str
    protocol: ProxyProtocol
    address: str
    username: Optional[str] = Field(default=None)
    password: Optional[str] = Field(default=None)
    last_check: Optional[str] = Field(alias="lastCheck")
    status: str
    created_at: Optional[str] = Field(alias="createdAt")
    updated_at: Optional[str] = Field(alias="updatedAt")
    group_id: str = Field(alias="groupId")

class CheckProxyResponse(BaseModel):
    status: str
    query: str
    country: str
    timezone: Optional[str] = Field(default=None)
    lat: Optional[str] = Field(default=None)
    lon: Optional[str] = Field(default=None)
    isp: Optional[str] = Field(default=None)
    city: Optional[str] = Field(default=None)
    region: Optional[str] = Field(default=None)