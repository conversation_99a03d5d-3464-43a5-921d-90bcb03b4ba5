import os
import shutil
from config.config import config
from typing import Optional
import logging

RESOURCES_DIRECTORY = "resources"

logger = logging.getLogger(__name__)

def check_or_create_directory(directory: str):
    if not os.path.exists(directory):
        os.makedirs(directory, mode=0o755)

def get_home_directory() -> str:
    user_home_dir = os.path.expanduser("~")
    return os.path.join(user_home_dir, f".{config.NAME_APP_FOLDER}")

def delete_folder(folder_path):
    if os.path.exists(folder_path):
        shutil.rmtree(folder_path)
    else:
        logger.warning(f"Folder {folder_path} does not exist.")

def clear_directory(directory: str):
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)  
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            logger.error(f'Failed to delete {file_path}. Reason: {e}')

def get_file_size(local_path: str) -> int:
    with open(local_path, 'rb') as file:
        return os.path.getsize(local_path)


def get_videos_dir() -> str:
    home_dir = get_home_directory()
    return os.path.join(home_dir, "download", "videos")

def get_music_dir() -> str:
    home_dir = get_home_directory()
    return os.path.join(home_dir, "download", "musics")

def get_render_dir() -> str:
    home_dir = get_home_directory()
    return os.path.join(home_dir, "render")




def get_resources_dir() -> str:
    home_dir = get_home_directory()
    return os.path.join(home_dir, RESOURCES_DIRECTORY)


def get_download_dir() -> str:
    home_dir = get_home_directory()
    return os.path.join(home_dir, "download")


def delete_file(file_path):
    try:
        os.remove(file_path)
    except Exception as e:
        pass

def read_file(file_path: str) -> tuple[str, Optional[BaseException]]:
    try:
        with open(file_path, 'r') as file:
            content = file.read()
            return content, None
    except BaseException as e:
        return "", e

def read_dir(dir_path: str) -> tuple[list[str], Optional[BaseException]]:
    try:
        return os.listdir(dir_path), None
    except BaseException as e:
        return [], e

