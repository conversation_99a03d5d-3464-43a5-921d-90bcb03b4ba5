from pydantic import BaseModel
from typing import Optional
from enum import Enum

class FileInfoState(BaseModel):
    name: str
    mimeType: str
    createdTime: str
    modifiedTime: str
    size: str

class ListFilesState(BaseModel):
    files: list[FileInfoState]
    nextPageToken: Optional[str] = None

class UploadFileState(BaseModel):
    kind: str
    id: str
    name: str
    mimeType: str
    teamDriveId: str
    driveId: str

class SharePublicState(BaseModel):
    kind: str
    id: str
    type: str
    role: str
    allowFileDiscovery: bool

class DownloadType(Enum):
    VIDEO = "video"
    MUSIC = "music"