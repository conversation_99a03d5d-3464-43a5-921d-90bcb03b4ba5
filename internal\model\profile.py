from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field

class ProfileType(str, Enum):
    UPDATE = "update"
    PING = "ping"
    COMPLETE = "complete"
    SYNCED = "synced"
    def __str__(self):
        return self.value

class ProfileStatus(str, Enum):
    PENDING = "pending"
    LIVE = "live"
    DIE = "die"
    ERROR = "error"
    VERIFY = "verify"
    DISABLED = "disabled"
    CHANNEL_DIE = "channelDie"
    def __str__(self):
        return self.value


class ProfileState(BaseModel):
    id: str
    core: str
    version: str
    email: str
    password: Optional[str] = Field(default=None)
    recovery: Optional[str] = Field(default=None)
    two_factor: Optional[str] = Field(alias="twoFactor", default=None)
    value: dict
    rotate: bool
    status: ProfileStatus
    is_running: bool = Field(alias="isRunning")
    created_at: str = Field(alias="createdAt")
    updated_at: str = Field(alias="updatedAt")
    last_check: Optional[str] = Field(alias="lastCheck", default=None)
    last_sync: Optional[str] = Field(alias="lastSync", default=None)
    last_used: Optional[str] = Field(alias="lastUsed", default=None)
    group_id: str = Field(alias="groupId")
    group_proxy_id: Optional[str] = Field(alias="groupProxyId", default=None)
    proxy_id: Optional[str] = Field(alias="proxyId", default=None)
    user_id: Optional[str] = Field(alias="userId", default=None)