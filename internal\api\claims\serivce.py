from typing import Optional
import logging
from config.config import config
from internal.api.claims.model import ProfileAction, TaskProfileChildrenState, TaskProfileState, TaskVideo2State, TaskVideoState
from config.setting import settings
from pkg.fetch_util import fetch

logger = logging.getLogger(__name__)



DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_CLAIM_KEY
}

async def fetch_task_profile() -> tuple[Optional[TaskProfileState], Optional[Exception]]:
    

    url: str = f"{config.API_CLAIM_URL}/api/task/profile"
    params = {}
    if settings.enable_parent_profile:
        params = {
            "isParent": "true"
        }
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    if result and not result.get('message'):
        task_profile = TaskProfileState(**result.get('data', {}))
        settings.is_parent = task_profile.action == ProfileAction.PARENT
        return task_profile, None
    reason = result.get('message', error)
    return None, Exception(reason)


async def fetch_task_profile_children(parent_id: str) -> tuple[Optional[TaskProfileChildrenState], Optional[Exception]]:
    url: str = f"{config.API_CLAIM_URL}/api/task/profile/children"
    params = {
        "parentId": parent_id
    }
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    if result and not result.get('message'):
        return TaskProfileChildrenState(**result.get('data', {})), None
    reason = result.get('message', error)
    return None, Exception(reason)


async def fetch_task_video2(profile_id: str) -> tuple[Optional[TaskVideo2State], Optional[Exception]]:
    url: str = f"{config.API_CLAIM_URL}/api/task/video2"
    params = {
        "profileId": profile_id,
    }
    logger.info(f"fetch_task_video url: {url}")
    logger.info(f"fetch_task_video params: {params}")
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    logger.info(f"fetch_task_video result: {result}")
    if result and not result.get('message'):
        return TaskVideo2State(**result.get('data', {})), None
    reason = result.get('message', error)
    return None, Exception(reason)


async def fetch_task_video(profile_id: str) -> tuple[Optional[TaskVideoState], Optional[Exception]]:
    url: str = f"{config.API_CLAIM_URL}/api/task/video2"
    params = {
        "profileId": profile_id,
    }
    logger.info(f"fetch_task_video url: {url}")
    logger.info(f"fetch_task_video params: {params}")
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    logger.info(f"fetch_task_video result: {result}")
    if result and not result.get('message'):
        return TaskVideoState(**result.get('data', {})), None
    reason = result.get('message', error)
    return None, Exception(reason)