


from typing import Optional
from internal.api.claims.model import PayloadUpdateLog, PayloadUpdateVideo, TaskProfileState
from internal.model.account import AccountState
from internal.model.profile import ProfileState
from pkg.fetch_util import fetch
from config.config import config
from pkg.result_model import Result
import logging
logger = logging.getLogger(__name__)

DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_CLAIM_KEY
}

async def fetch_profile_account(profile_id: str) -> tuple[Optional[ProfileState], Optional[AccountState], Optional[Exception]]:
    try:
        url: str = f"{config.API_URL}/api/profile/auto/upload/{profile_id}?force=true"
        result, error = await fetch(url, headers=DEFAULT_HEADERS)
        if result and result.get('success'):
            profile = ProfileState(**result.get('data', {}).get('profile', {}))
            account = AccountState(**result.get('data', {}).get('account', {}))
            return profile, account, None
        reason = result.get('message', error)
        return None, None, Exception(reason)
    except Exception as e:
        logger.critical(f"catch error fetch_profile_account: {e}")
        return None, None, e
    
async def fetch_profile_with(email: str) -> Result[TaskProfileState, Exception]:
    try:
        url: str = f"{config.API_CLAIM_URL}/api/profile/findByEmail"
        params = {
            "email": email
        }
        logger.info(f"fetch_profile url: {url}")
        result, error = await fetch(url, headers=DEFAULT_HEADERS, params=params)
        if result and result.get('success'):
            profile = TaskProfileState(**result.get('data', {}))
            return Result(value=profile)
        reason = result.get('message', error)
        return Result(error=Exception(reason))
    except Exception as e:
        logger.critical(f"catch error fetch_profile: {e}")
        return Result(error=e)
    
async def release_profile(profile_id: str) -> Optional[Exception]:
    url: str = f"{config.API_CLAIM_URL}/api/task/profile/release"
    params = {
        "profileId": profile_id
    }
    logger.info(f"release_profile: {profile_id}")
    result, error = await fetch(url, method="GET", headers=DEFAULT_HEADERS, params=params)
    if result and not result.get('error'):
        return None
    reason = result.get('message', error)
    return Exception(reason)

async def update_video(payload: PayloadUpdateVideo) -> Optional[Exception]:
    url: str = f"{config.API_CLAIM_URL}/api/update/video"
    logger.warning(f"update_video_payload: {payload}")
    result, error = await fetch(url, method="POST", json=payload.model_dump(exclude_none=True), headers=DEFAULT_HEADERS)
    logger.warning(f"update_video_result: {result}")
    if result and not result.get('error'):
        return None
    reason = result.get('message', error)
    return Exception(reason)

async def update_log(payload: PayloadUpdateLog) -> Optional[Exception]:
    url: str = f"{config.API_CLAIM_URL}/api/update/log"
    logger.warning(f"update_log_payload: {payload}")
    result, error = await fetch(url, method="POST", json=payload.model_dump(exclude_none=True), headers=DEFAULT_HEADERS)
    logger.warning(f"update_log_result: {result}")
    if result and not result.get('error'):
        return None
    reason = result.get('message', error)
    return Exception(reason)