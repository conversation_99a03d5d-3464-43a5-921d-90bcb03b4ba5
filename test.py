
import random
import subprocess
from time import sleep, time
import uiautomator2 as u2
import logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

d = u2.connect('127.0.0.1:5557') # connect to device
print(d.info)

# d.screen_off()
# sleep(5)
# d.screen_on()

def upload_autiomation(d: u2.Device):
    if d(resourceId="com.android.systemui:id/lock_icon").exists:
        logger.info("Màn hình đang bị khóa")
        d(resourceId="com.android.systemui:id/lock_icon").click()
        sleep(1)
    
        d(resourceId="com.android.systemui:id/key1").click()
        d(resourceId="com.android.systemui:id/key1").click()
        d(resourceId="com.android.systemui:id/key1").click()
        d(resourceId="com.android.systemui:id/key1").click()
        d(resourceId="com.android.systemui:id/key_enter").click()
    
    else:
        logger.info("<PERSON>àn hình đang mở")

    # Kill youtube app
    d.shell(["am", "force-stop", "com.google.android.youtube"])
    count_try = 0
    while True:
        elYTB = d(text="YouTube", className="android.widget.TextView").wait(timeout=5.0)
        if elYTB:
            sleep(1)
            d(text="YouTube", className="android.widget.TextView").click()
            break
        else:
            if count_try > 3:
                logger.info("Không tìm thấy YouTube")
                break
            count_try += 1
            d.press("home")
    
    
    # click profile
    el = d.xpath('//*[@resource-id="com.google.android.youtube:id/pivot_bar_thumbnail"]')
    el.wait(timeout=30.0)
    sleep(random.uniform(3, 5))
    if el.exists:
        logger.info("Tìm thấy nút Profile")
        el.click()
    else:
        logger.info("Không tìm thấy nút Profile")
        raise Exception("Không tìm thấy nút Profile")
    
    
    
    # Click switch account
    el = d.xpath('//*[@content-desc="Switch account"]')
    el.wait(timeout=30.0)
    sleep(random.uniform(3, 5))
    if el.exists:
        logger.info("Tìm thấy nút Switch Account")
        el.click()
    else:
        logger.info("Không tìm thấy nút Switch Account")
        raise Exception("Không tìm thấy nút Switch Account")
    
    google_account_header_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/google_account_header"]')
    google_account_header_el.wait(timeout=30.0)
    sleep(random.uniform(3, 5))
    if google_account_header_el.exists:
        logger.info("Tìm thấy nút Google Account Header")
    else:
        logger.info("Không tìm thấy nút Google Account Header")
        raise Exception("Không tìm thấy nút Google Account Header")
    
    
    d(scrollable=True).scroll.to(text="@SYLVIATMIRANDA")
    sleep(random.uniform(3, 5))
    el = d.xpath('//*[@text="@SYLVIATMIRANDA"]')
    el.wait(timeout=30.0)
    sleep(random.uniform(3, 5))
    if el.exists:
        logger.info("Tìm thấy nút Account")
        el.click()
    else:
        logger.info("Không tìm thấy nút Account")
        raise Exception("Không tìm thấy nút Account")
    
    # Click got it when switch account if show
    got_it_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/bottom_button_row"]')
    got_it_button_el.wait(timeout=10.0)
    sleep(random.uniform(3, 5))
    if got_it_button_el.exists:
        logger.info("Tìm thấy nút Got it")
        got_it_button_el.click()
    else:
        logger.info("Không tìm thấy nút Got it")
        
    
    # Open music 
    sleep(random.uniform(3, 5))
    url = "https://www.youtube.com/source/Z3F19CxgA2U/shorts"
    command = ["adb", "-s", "127.0.0.1:5557", "shell", "am", "start", "-a", "android.intent.action.VIEW", url]
    #com.google.android.youtube:id/mealbar_dismiss_button
    # Chạy lệnh
    subprocess.run(command)
    
    sleep(random.uniform(3, 5))
    open_with_youtube_el = d.xpath('//android.widget.TextView[@text="YouTube"]')
    open_with_youtube_el.wait(timeout=10.0)
    
    if open_with_youtube_el.exists:
        logger.info("Tìm thấy nút Open with YouTube")
        open_with_youtube_el.click()
        
        always_button_el = d.xpath('//*[@resource-id="android:id/button_always"]')
        always_button_el.wait(timeout=30.0)
        sleep(random.uniform(3, 5))
        if always_button_el.exists:
            logger.info("Tìm thấy nút Always")
            always_button_el.click()
        else:
            logger.info("Không tìm thấy nút Always")
            raise Exception("Không tìm thấy nút Always in Open with YouTube")
        
    else:
        logger.info("Không tìm thấy nút Open with YouTube")
        
        
    
    # check button Use this sound if exist
    use_this_sound_el = d.xpath('//*[@content-desc="Use this sound"]')
    use_this_sound_el.wait(timeout=30.0)
    sleep(random.uniform(3, 5))
    if use_this_sound_el.exists:
        logger.info("Tìm thấy nút Use this sound")
        use_this_sound_el.click()
    else:
        logger.info("Không tìm thấy nút Use this sound")
        raise Exception("Không tìm thấy nút Use this sound when open music")
    
    

    el = d.xpath('//*[@resource-id="com.google.android.youtube:id/action_bar_root"]')
    el.wait(30)
    if el.exists == False:
        logger.info("Không tìm thấy view action_bar_root")
        raise Exception("Không tìm view nút action_bar_root")
        
    
    sleep(random.uniform(3, 5))
    timeout = 30 # second
    while True:
        sleep(2)
        el = d.xpath('//*[@resource-id="com.google.android.youtube:id/loading_spinner"]')
        if el.exists:
            logger.info("Found processing_indicator_spinner, loading music")
        else:
            break
        if timeout <= 0:
            logger.info("Timeout when loading music")
            raise Exception("Timeout when loading music")
        timeout -= 2
        
    
    
    # Click camera gallery
    sleep(random.uniform(3, 5))
    camera_gallery_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button_delegate"]')
    camera_gallery_el.wait(timeout=30.0)
    if camera_gallery_el.exists:
        logger.info('Tìm thấy nút Camera Gallery')
        camera_gallery_el.click()
    else:
        logger.info('Không tìm thấy nút Camera Gallery')
    

    # CLick select video in gallery
    select_badge_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_badge"]')
    sleep(random.uniform(3, 5))
    select_badge_el.wait(timeout=5.0)

    if select_badge_el.exists:
        logger.info("Found button Select")
        select_badge_el.click()
    else:
        logger.info("Not found button Select")
        raise Exception("Not found button Select")

    # Click Next button after select video
    select_next_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_next_button"]')
    sleep(random.uniform(3, 5))
    select_next_button_el.wait(timeout=5.0)
    if select_next_button_el.exists:
        logger.info("Found button Next after select video")
        select_next_button_el.click()
    else:
        logger.info("Not found button Next after select video")
        raise Exception("Not found button Next after select video")

    


    # click done upload 
    click_done_upload_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_trim_finish_trim_button"]')
    sleep(random.uniform(3, 5))
    click_done_upload_el.wait(timeout=5.0)
    if click_done_upload_el.exists:
        logger.info("Found button Done upload")
        click_done_upload_el.click()
    else:
        logger.info("Not found button Done upload")
        raise Exception("Not found button Done upload")

    # Wait for upload waiting for indicator not found
    while True:
        sleep(1)
        el = d(resourceId="com.google.android.youtube:id/processing_indicator_spinner")
        if el.exists:
            logger.info("Found processing_indicator_spinner, đang upload")
        else:
            break

    # Click next button after upload
    logger.info("Finish upload")
    shorts_camera_next_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]')
    sleep(random.uniform(3, 5))
    shorts_camera_next_button_el.wait(timeout=5.0)
    if shorts_camera_next_button_el.exists:
        logger.info("Found button shorts_camera_next_button")
        shorts_camera_next_button_el.click()
    else:
        logger.info("Not found button shorts_camera_next_button")
        raise Exception("Not found button shorts_camera_next_button")
    
    
    # Click volume button after upload finish
    el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_edit_volume_button"]')
    sleep(random.uniform(3, 5))
    el.wait(timeout=30.0)
    if el.exists:
        logger.info("Tìm thấy nút Volume")
        el.click()
    else:
        logger.info("Không tìm thấy nút Volume")
        raise Exception("Không tìm thấy nút Volume")
    




    volume_track_el = d(resourceId="com.google.android.youtube:id/volume_track")
    sleep(random.uniform(3, 5))
    volume_track_el.wait(timeout=5.0)
    if volume_track_el.exists:
        logger.info("Found volume_track")
        # Check slider exists
        slider = volume_track_el.child(resourceId="com.google.android.youtube:id/slider")
        slider.wait(timeout=5.0)  
        sleep(random.uniform(3, 5))
        # Check slider exists in volume_track_el, if not found, raise exception
        if slider.exists:
            logger.info("Found slider")
            bounds = slider.info['bounds']
            x1 = bounds['left']
            x2 = bounds['right']
            y = (bounds['top'] + bounds['bottom']) // 2  # y cố định giữa thanh

            # Swipe from left to right
            d.swipe(x1, y, x2, y, duration=0.3)
            logger.info("Swipe slider to max")
        else:
            logger.info("Not found slider")
            raise Exception("Not found slider")
    else:
        logger.info("Not found volume_track")
        raise Exception("Not found volume_track")
    
    
    shorts_music_volume_control_el = d(resourceId="com.google.android.youtube:id/shorts_music_volume_control")
    sleep(random.uniform(3, 5))
    shorts_music_volume_control_el.wait(timeout=5.0)
    if shorts_music_volume_control_el.exists:
        print("Tìm thấy thanh shorts_music_volume_control")
        logger.info("Found shorts_music_volume_control")
        # Found slider in shorts_music_volume_control
        slider = shorts_music_volume_control_el.child(resourceId="com.google.android.youtube:id/slider")
        slider.wait(timeout=5.0)  
        sleep(random.uniform(3, 5))
        # Check slider exists in shorts_music_volume_control, if not found, raise exception
        if slider.exists:
            logger.info("Found slider in shorts_music_volume_control")
            track_bounds = slider.info['bounds']
            track_x1 = track_bounds['left']
            track_x2 = track_bounds['right']
            y = (track_bounds['top'] + track_bounds['bottom']) // 2

            total_width = track_x2 - track_x1
            target_x = track_x1 + int(total_width * 0.19)

            
            # track_x2 - 9 để tránh chạm vào thanh vuốt
            # Swipe from right to left 20%
            d.swipe(track_x2 - 9, y, target_x, y, duration=0.3)
        else:
            logger.info("Not found slider in shorts_music_volume_control")
            raise Exception("Not found slider in shorts_music_volume_control")
    else:
        logger.info("Not found shorts_music_volume_control")
        raise Exception("Not found shorts_music_volume_control")
    
    # Click done edit volume
    click_done_edit_volume_el = d.xpath('//*[@content-desc="Done"]')
    sleep(random.uniform(3, 5))
    click_done_edit_volume_el.wait(timeout=5.0)
    if click_done_edit_volume_el.exists:
        logger.info("Found button Done")
        click_done_edit_volume_el.click()
    else:
        logger.info("Not found button Done")
        raise Exception("Not found button Done")
    
    
    # Click short post bottom button 
    shorts_post_bottom_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_post_bottom_button"]')
    sleep(random.uniform(3, 5))
    shorts_post_bottom_button_el.wait(timeout=5.0)
    if shorts_post_bottom_button_el.exists:
        logger.info("Found button Post")
        shorts_post_bottom_button_el.click()
    else:
        logger.info("Not found button Post")
        raise Exception("Not found button Post")
    
    
    # Sceen Location
    # Click location
    sleep(random.uniform(3, 5))
    recycler_view_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/recycler_view"]/android.view.ViewGroup/android.view.ViewGroup')
    recycler_view_el.wait(timeout=30.0)
    if recycler_view_el.exists:
        logger.info("Found recycler view")
    else:
        logger.info("Not found recycler view")
        raise Exception("Not found recycler view")

    list_view_group = recycler_view_el.all()
    view_group_count = len(list_view_group)
    if view_group_count < 4:
        logger.info("Not enough list_view_group in recycler view")
        raise Exception("Not enough list_view_group in recycler view")

    list_view_group[3].click()

    # Set location 
    sleep(random.uniform(3, 5))
    search_place_el = d.xpath('//android.widget.EditText[@text="Search place"]')
    search_place_el.wait(timeout=30.0)
    if search_place_el.exists:
        logger.info("Found search place")
        search_place_el.click()
        localtion_text_list = ["USA", "United States"]
        text = random.choice(localtion_text_list)
        d.send_keys(text, clear=True)

    else:
        logger.info("Not found search place")
        raise Exception("Not found search place")


    sleep(random.uniform(3, 5))
    result_search_scroll_view = d.xpath('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup')
    result_search_scroll_view.wait(timeout=30.0)
    if result_search_scroll_view.exists:
        logger.info("Found result search scroll view")
    else:
        logger.info("Not found result search scroll view")
        raise Exception("Not found result search scroll view")

    list_view_group = result_search_scroll_view.all()
    view_group_count = len(list_view_group)
    if view_group_count < 6:
        logger.info("Not enough result search scroll view")
        raise Exception("Not enough result search scroll view")

    list_view_group[0].click()


    # Click select audience
    sleep(random.uniform(3, 5))
    recycler_view_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/recycler_view"]/android.view.ViewGroup/android.view.ViewGroup')
    recycler_view_el.wait(timeout=5.0)
    if recycler_view_el.exists:
        logger.info("Found recycler view")
    else:
        logger.info("Not found recycler view")
        raise Exception("Not found recycler view")

    list_view_group = recycler_view_el.all()
    view_group_count = len(list_view_group)
    if view_group_count < 5:
        logger.info("Not enough list_view_group in recycler view")
        raise Exception("Not enough list_view_group in recycler view")

    list_view_group[4].click()

    # Screen child select audience
    sleep(random.uniform(3, 5))
    recycler_view_el = d.xpath('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup')
    recycler_view_el.wait(timeout=30.0)
    if recycler_view_el.exists:
        logger.info("Found recycler view in child select audience")
    else:
        logger.info("Not found recycler view in child select audience")
        raise Exception("Not found recycler view in child select audience")

    list_view_group = recycler_view_el.all()
    view_group_count = len(list_view_group)
    if view_group_count < 4:
        logger.info("Not enough list_view_group in recycler view")
        raise Exception("Not enough list_view_group in recycler view")

    NOT_MADE_FOR_KIDS_INDEX = 2 
    list_view_group[NOT_MADE_FOR_KIDS_INDEX].click()


    sleep(random.uniform(3, 5))
    back_button_el = d.xpath('//*[@content-desc="Back"]')
    back_button_el.wait(timeout=30.0)
    if back_button_el.exists:
        logger.info("Found button Back")
        back_button_el.click()
    else:
        logger.info("Not found button Back")
        raise Exception("Not found button Back")
    
    # Input text in Edit video 
    edit_text_el = d.xpath('//android.widget.EditText')
    sleep(random.uniform(3, 5))
    edit_text_el.wait(timeout=60.0)
    if edit_text_el.exists:
        logger.info("Found edit text")
        edit_text_el.click()
        d.clear_text()
        d.send_keys("Hotel Computer Whiz Finds Something Spooky#spooky #computer #hotel #ghost", clear=True)  # Gõ từng ký tự, kể cả in hoa
    else:
        logger.info("Not found edit text")
        raise Exception("Not found edit text")
    
    # Click Upload short button 
    short_upload_button_el = d.xpath('//*[@resource-id="com.google.android.youtube:id/upload_bottom_button_container"]')
    sleep(random.uniform(3, 5))
    short_upload_button_el.wait(timeout=60.0)
    if short_upload_button_el.exists:
        logger.info("Found button Upload short")
        short_upload_button_el.click()
    else:
        logger.info("Not found button Upload short")
        raise Exception("Not found button Upload short")


# # Input text in Edit video 
# el = d.xpath('//android.widget.EditText')
# el.wait(timeout=5.0)
# d.set_fastinput_ime(True)
# sleep(random.uniform(3, 5))
# if el.exists:
#     el.click()
#     d.clear_text()
#     d.send_keys("Hello World", clear=True)  # Gõ từng ký tự, kể cả in hoa
# else:
#     print("Không tìm thấy ô nhập.")

# # Khôi phục lại bàn phím mặc định nếu cần
# d.set_fastinput_ime(False)







# Click create button
upload_autiomation(d)









# # proxy_address = "***********:46816"

# # try:
# #     result = subprocess.run(
# #         ["adb", "shell", "settings", "put", "global", "http_proxy", proxy_address],
# #         capture_output=True,
# #         text=True,
# #         check=True
# #     )
# #     print("Proxy set successfully.")
# # except subprocess.CalledProcessError as e:
# #     print(f"Error setting proxy: {e.stderr}")

# el = d.xpath('//*[@content-desc="Use this sound"]')
# el.wait(timeout=30.0)
# sleep(random.uniform(3, 5))
# if el.exists:
#     print("Tìm thấy nút Use this sound")
#     bounds = el.info['bounds']
#     el.click()
# else:
#     print("Không tìm thấy nút Use this sound")
#     sleep(2)
#     el = d.xpath('//*[@content-desc="Use this sound"]')
#     exit()



# sleep(3)

# url = "https://www.youtube.com/source/Z3F19CxgA2U/shorts"
# command = ["adb", "shell", "am", "start", "-a", "android.intent.action.VIEW", url]
# #com.google.android.youtube:id/mealbar_dismiss_button

# # Chạy lệnh
# subprocess.run(command)



# el = d.xpath('//*[@content-desc="Use this sound"]')
# el.wait(timeout=30.0)
# sleep(random.uniform(3, 5))
# if el.exists:
#     print("Tìm thấy nút Use this sound")
#     bounds = el.info['bounds']
#     el.click()
# else:
#     print("Không tìm thấy nút Use this sound")
#     sleep(2)
#     el = d.xpath('//*[@content-desc="Use this sound"]')
#     exit()




# # elYTB = d(text="YouTube", className="android.widget.TextView").wait(timeout=5.0)

# # if elYTB:
# #     sleep(1)
# #     print("Tìm thấy YouTube")
# #     d(text="YouTube", className="android.widget.TextView").click()
# # else:
# #     print("Không tìm thấy YouTube")
# #     d.press("home")
# #     sleep(1)
# #     d(text="YouTube", className="android.widget.TextView").click()
# #     sleep(1)
# #     try:
# #         d(text="YouTube", className="android.widget.TextView").click()
# #         #d(resourceId="com.google.android.youtube:id/mealbar_dismiss_button").click()
# #     except Exception as e:
# #         print(e)

# # # click create button
# # el = d.xpath('//*[@content-desc="Create"]')
# # el.wait(timeout=5.0)
# # sleep(random.uniform(3, 5))
# # if el.exists:
# #     print("Tìm thấy nút Create")
# #     bounds = el.info['bounds']
# #     el.click()
    


# # # add file upload 
# # local_file = "1.webm"
# # target_path = f"/sdcard/Pictures/{local_file}"

# # # Bước 1: Xóa toàn bộ file trong Camera
# # subprocess.run(["adb", "shell", "rm", "-rf", "/sdcard/Pictures*"], check=True)

# # print("Đã xóa ảnh/video cũ")
# # result = d.shell("ls -la /sdcard/Pictures/")

# # In kết quả ra console
# # print("Nội dung thư mục /sdcard/Pictures/:")
# # print(result.output)


# # Bước 2: Đẩy file mới vào
# # subprocess.run(["adb", "push", local_file, target_path], check=True)
# # print(f"Đã copy file {local_file} vào Camera")

# # Bước 3: Refresh Gallery
# # subprocess.run([
# #     "adb",
# #     "shell", "am", "broadcast",
# #     "-a", "android.intent.action.MEDIA_SCANNER_SCAN_FILE",
# #     "-d", f"file:///sdcard"
# # ])
# # print("✅ Đã refresh Gallery qua adb")
# # print("Đã refresh Gallery để thấy file mới")



# el = d.xpath('//*[@content-desc="Use this sound"]')
# el.wait(timeout=30.0)
# sleep(random.uniform(3, 5))
# if el.exists:
#     print("Tìm thấy nút Use this sound")
#     bounds = el.info['bounds']
#     el.click()
# else:
#     print("Không tìm thấy nút Use this sound")
#     sleep(2)
#     el = d.xpath('//*[@content-desc="Use this sound"]')
#     exit()



# #Click camera gallery
# el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button"]')

# el.wait(timeout=30.0)
# sleep(random.uniform(3, 5))
# if el.exists:
#     print("Tìm thấy nút Camera Gallery")
#     bounds = el.info['bounds']
#     el.click()
# else:
#     print("Không tìm thấy nút Camera Gallery")
#     sleep(2)
#     el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button"]')
#     exit()




    
# upload bị lỗi 
#com.google.android.youtube:id/processing_indicator_spinner
        

    
    # //*[@resource-id="com.google.android.youtube:id/shorts_duration_button"]
    
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_music_button"]
    
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]
    
    