
import random
import subprocess
from time import sleep, time
import uiautomator2 as u2

d = u2.connect('127.0.0.1:5557') # connect to device
print(d.info)

d.screen_off()
sleep(5)
d.screen_on()


# <PERSON><PERSON><PERSON> tra xem biểu tượng khóa có hiện trên màn hình không
if d(resourceId="com.android.systemui:id/lock_icon").exists:
    print("🔒 Màn hình đang bị khóa")
    d(resourceId="com.android.systemui:id/lock_icon").click()
    sleep(1)
    
    d(resourceId="com.android.systemui:id/key1").click()
    d(resourceId="com.android.systemui:id/key1").click()
    d(resourceId="com.android.systemui:id/key1").click()
    d(resourceId="com.android.systemui:id/key1").click()
    d(resourceId="com.android.systemui:id/key_enter").click()
    
else:
    print("🔓 <PERSON><PERSON><PERSON> hình đang mở")

d.shell(["am", "force-stop", "com.google.android.youtube"])

proxy_address = "***********:46816"

try:
    result = subprocess.run(
        ["adb", "shell", "settings", "put", "global", "http_proxy", proxy_address],
        capture_output=True,
        text=True,
        check=True
    )
    print("Proxy set successfully.")
except subprocess.CalledProcessError as e:
    print(f"Error setting proxy: {e.stderr}")


sleep(3)

url = "https://www.youtube.com/source/Z3F19CxgA2U/shorts"
command = ["adb", "shell", "am", "start", "-a", "android.intent.action.VIEW", url]
#com.google.android.youtube:id/mealbar_dismiss_button

# Chạy lệnh
subprocess.run(command)

# elYTB = d(text="YouTube", className="android.widget.TextView").wait(timeout=5.0)

# if elYTB:
#     sleep(1)
#     print("Tìm thấy YouTube")
#     d(text="YouTube", className="android.widget.TextView").click()
# else:
#     print("Không tìm thấy YouTube")
#     d.press("home")
#     sleep(1)
#     d(text="YouTube", className="android.widget.TextView").click()
#     sleep(1)
#     try:
#         d(text="YouTube", className="android.widget.TextView").click()
#         #d(resourceId="com.google.android.youtube:id/mealbar_dismiss_button").click()
#     except Exception as e:
#         print(e)

# # click create button
# el = d.xpath('//*[@content-desc="Create"]')
# el.wait(timeout=5.0)
# sleep(random.uniform(3, 5))
# if el.exists:
#     print("Tìm thấy nút Create")
#     bounds = el.info['bounds']
#     el.click()
    


# # add file upload 
# local_file = "1.webm"
# target_path = f"/sdcard/Pictures/{local_file}"

# # Bước 1: Xóa toàn bộ file trong Camera
# subprocess.run(["adb", "shell", "rm", "-rf", "/sdcard/Pictures*"], check=True)

# print("Đã xóa ảnh/video cũ")
# result = d.shell("ls -la /sdcard/Pictures/")

# In kết quả ra console
# print("Nội dung thư mục /sdcard/Pictures/:")
# print(result.output)


# Bước 2: Đẩy file mới vào
# subprocess.run(["adb", "push", local_file, target_path], check=True)
# print(f"Đã copy file {local_file} vào Camera")

# Bước 3: Refresh Gallery
# subprocess.run([
#     "adb",
#     "shell", "am", "broadcast",
#     "-a", "android.intent.action.MEDIA_SCANNER_SCAN_FILE",
#     "-d", f"file:///sdcard"
# ])
# print("✅ Đã refresh Gallery qua adb")
# print("Đã refresh Gallery để thấy file mới")



el = d.xpath('//*[@content-desc="Use this sound"]')
el.wait(timeout=30.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Use this sound")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Use this sound")
    sleep(2)
    el = d.xpath('//*[@content-desc="Use this sound"]')
    exit()



#Click camera gallery
el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button"]')

el.wait(timeout=30.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Camera Gallery")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Camera Gallery")
    sleep(2)
    el = d.xpath('//*[@resource-id="com.google.android.youtube:id/reel_camera_gallery_button"]')
    exit()


# CLick select 
el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_badge"]')
el .wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Select")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Select")
    sleep(2)
    el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_badge"]')
    exit()

# Click Next 
el = d.xpath('//*[@resource-id="com.google.android.youtube:id/multi_select_next_button"]')
el.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Next")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Next")
    exit()



# clic done upload
el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_trim_finish_trim_button"]')
el.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Done")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Done")
    exit()


while True:
    sleep(1)
    el = d(resourceId="com.google.android.youtube:id/processing_indicator_spinner")
    if el.exists:
        print("Tìm thấy nút Shorts Duration")
    else:
        break

print("Đã upload xong")


el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]')
el.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Next")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Next")
    exit()


el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_edit_volume_button"]')
el.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Volume")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Volume")
    exit()
    




volume_track = d(resourceId="com.google.android.youtube:id/volume_track")
volume_track.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if volume_track.exists:
    print("Tìm thấy thanh Volume")
    # Tìm phần tử con slider bên trong volume_track
    slider = volume_track.child(resourceId="com.google.android.youtube:id/slider")
    slider.wait(timeout=5.0)  
    sleep(random.uniform(3, 5))
    # Kiểm tra nếu tìm thấy slider
    if slider.exists:
        print("Tìm thấy slider!")
        print("Thông tin:", slider.info)
        bounds = slider.info['bounds']
        x1 = bounds['left']
        x2 = bounds['right']
        y = (bounds['top'] + bounds['bottom']) // 2  # y cố định giữa thanh

        # Kéo từ đầu đến cuối (trượt sang phải hết cỡ)
        d.swipe(x1, y, x2, y, duration=0.2)
        print("Đã kéo slider đến mức tối đa.")
    else:
        print("Không tìm thấy slider bên trong volume_track.")
else:
    print("Không tìm thấy thanh Volume")
    exit()
    
    
shorts_music_volume_control = d(resourceId="com.google.android.youtube:id/shorts_music_volume_control")
shorts_music_volume_control.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if shorts_music_volume_control.exists:
    print("Tìm thấy thanh shorts_music_volume_control")
    # Tìm phần tử con slider bên trong volume_track
    slider = shorts_music_volume_control.child(resourceId="com.google.android.youtube:id/slider")
    slider.wait(timeout=5.0)  
    sleep(random.uniform(3, 5))
    # Kiểm tra nếu tìm thấy slider
    if slider.exists:
        print("Tìm thấy slider!")
        # Lấy vùng điều chỉnh (volume track)
        track_bounds = slider.info['bounds']
        track_x1 = track_bounds['left']
        track_x2 = track_bounds['right']
        y = (track_bounds['top'] + track_bounds['bottom']) // 2

        total_width = track_x2 - track_x1
        target_x = track_x1 + int(total_width * 0.2)

        # Swipe từ gần bên phải về 20%
        d.swipe(track_x2 - 9, y, target_x, y, duration=0.3)

# click done edit volume
el = d.xpath('//*[@content-desc="Done"]')
el.wait(timeout=10.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Done")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Done")
    exit()
    
    
# Click short post bottom button 
el = d.xpath('//*[@resource-id="com.google.android.youtube:id/shorts_post_bottom_button"]')
el.wait(timeout=5.0)
sleep(random.uniform(3, 5))
if el.exists:
    print("Tìm thấy nút Post")
    bounds = el.info['bounds']
    el.click()
else:
    print("Không tìm thấy nút Post")
    exit()
    

# Input text in Edit video 
el = d.xpath('//android.widget.EditText')
el.wait(timeout=5.0)
d.set_fastinput_ime(True)
sleep(random.uniform(3, 5))
if el.exists:
    el.click()
    d.clear_text()
    d.send_keys("Hello World", clear=True)  # Gõ từng ký tự, kể cả in hoa
else:
    print("Không tìm thấy ô nhập.")

# Khôi phục lại bàn phím mặc định nếu cần
d.set_fastinput_ime(False)

    
# upload bị lỗi 
#com.google.android.youtube:id/processing_indicator_spinner
        

    
    # //*[@resource-id="com.google.android.youtube:id/shorts_duration_button"]
    
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_music_button"]
    
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]
    
    # //*[@resource-id="com.google.android.youtube:id/shorts_camera_next_button"]
    
    