import subprocess

import logging

from pkg.result_model import Result
logger = logging.getLogger(__name__)

def run_command_check_result(command: str, success_keyword: str =None, fail_keyword: str =None, is_log: bool=True) -> Result[dict, Exception]:
    """
    Ch<PERSON><PERSON> lệnh, in log theo thời gian thực, kiểm tra dòng cuối hoặc keyword để xác định thành công/thất bại.

    Args:
        command (str or list): Lệnh shell.
        success_keyword (str): Từ khóa để xác định thành công.
        fail_keyword (str): Từ khóa để xác định thất bại.

    Returns:
        dict: {
            'returncode': int,
            'last_line': str,
            'status': 'success' | 'fail' | 'unknown'
        }
    """
    process = subprocess.Popen(
        command,
        shell=isinstance(command, str),
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    logger.info(f"Running command: {command}")

    last_line = ''
    try:
        for line in process.stdout:
            line = line.strip()
            if line:
                last_line = line
                if is_log:
                    logger.info(line)
        process.wait()

        # Xác định trạng thái dựa trên dòng cuối hoặc từ khóa
        status = 'unknown'
        if success_keyword and success_keyword in last_line:
            status = 'success'
        elif fail_keyword and fail_keyword in last_line:
            status = 'fail'
        elif process.returncode == 0:
            status = 'success'
        else:
            status = 'fail'

        return Result(value= {
            'returncode': process.returncode,
            'last_line': last_line,
            'status': status
        })

    except Exception as e:
        logger.error(f"Error running command: {e}")
        process.kill()
        return Result(error=e, value={
            'returncode': -1,
            'last_line': '',
            'status': 'fail'
        })