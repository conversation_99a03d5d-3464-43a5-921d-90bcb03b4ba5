from config.config import config

from typing import Optional
import logging

from internal.model.google import GoogleState
from internal.model.teamdrive import TeamdriveState
from pkg.fetch_util import fetch

logger = logging.getLogger(__name__)

DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_CLAIM_KEY
}

async def fetch_google() -> tuple[Optional[GoogleState], Optional[Exception]]:
    try:
        url: str = f"{config.API_CLAIM_URL}/api/google"
        params = {
            "type": "drive",
            "json": "true"
        }
        result, error = await fetch(url, headers=DEFAULT_HEADERS, params=params)
        if result and result.get('success'):
            return GoogleState(**result.get('data', {})), None
        reason = result.get('message', error)
        return None, Exception(reason)
    except Exception as e:
        logger.critical(f"catch error fetch_google: {e}")
        return None, e
    
async def fetch_teamdrive() -> tuple[Optional[TeamdriveState], Optional[Exception]]:
    try:
        url: str = f"{config.API_CLAIM_URL}/api/teamdrive"   
        result, error = await fetch(url, headers=DEFAULT_HEADERS)
        if result and result.get('success'):
            return TeamdriveState(**result.get('data', {})), None
        reason = result.get('message', error)
        return None, Exception(reason)
    except Exception as e:
        logger.critical(f"catch error fetch_teamdrive: {e}")
        return None, e