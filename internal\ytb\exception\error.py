
class VerifyItsYouError(Exception):
    def __init__(self, message="Verify its you"):
        self.message = message
        super().__init__(self.message)

class DownloadVideoError(Exception):
    def __init__(self, message="Download video error"):
        self.message = message
        super().__init__(self.message)

class DownloadMusicError(Exception):
    def __init__(self, message="Download music error"):
        self.message = message
        super().__init__(self.message)

class RenderVideoError(Exception):
    def __init__(self, message="Render video error"):
        self.message = message
        super().__init__(self.message)

class VoiceSeparationError(Exception):
    def __init__(self, message="Voice separation error"):
        self.message = message
        super().__init__(self.message)
        
        

class NotGetTaskChildrenError(Exception):
    def __init__(self, message="Not get task children error"):
        self.message = message
        super().__init__(self.message)
        
class ProxyError(Exception):
    def __init__(self, message="Proxy error"):
        self.message = message
        super().__init__(self.message)
        
