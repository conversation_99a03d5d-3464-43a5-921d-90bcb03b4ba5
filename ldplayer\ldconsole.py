import subprocess
from typing import List

from ldplayer.model import LDPlayerInstance
from pkg.result_model import Result
import logging



from pkg.util import is_email
logger = logging.getLogger(__name__)
from . import LD_CONSOLE_CMD

def get_ldplayer_list() -> Result[List[LDPlayerInstance], Exception]:
    try:
        result = subprocess.run(
            [LD_CONSOLE_CMD, "list2"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode != 0:
            raise RuntimeError(f"Lỗi khi chạy ldconsole: {result.stderr}")

        lines = result.stdout.strip().splitlines()
        instances = []
        for line in lines:
            parts = line.strip().split(",")
            if len(parts) != 10:
                logger.error(f"Sai định dạng dòng: {line}")
                continue  # bỏ qua dòng sai định dạng

            data = {
                "index": int(parts[0]),
                "title": parts[1],
                "top_window_handle": int(parts[2]),
                "bind_window_handle": int(parts[3]),
                "android_started": int(parts[4]),
                "pid": int(parts[5]),
                "vbox_pid": int(parts[6]),
                "width": int(parts[7]),
                "height": int(parts[8]),
                "dpi": int(parts[9]),
            }
        

            instance = LDPlayerInstance(**data)
            instances.append(instance)
        return Result(value=instances)
    except Exception as e:
        return Result(error=e)
    

def stop_ldplayer_by_id(index: int) -> Result[bool, Exception]:
    try:
        result = subprocess.run(
            ["ldconsole", "quit", "--index", str(index)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        if result.returncode == 0:
            return Result(value=True)
        else:
            return Result(error=result.stderr)
    except Exception as e:
        return Result(error=e)
    

def start_ldplayer_by_id(index: int) -> Result[bool, Exception]:
    try:
        result = subprocess.run(
            ["ldconsole", "launch", "--index", str(index)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        if result.returncode == 0:
            return Result(value=True)
        else:
            return Result(error=result.stderr)
    except Exception as e:
        return Result(error=e)
