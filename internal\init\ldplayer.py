


from ldplayer.ldconsole import get_ldplayer_list, stop_ldplayer_by_id
import logging

from ldplayer.model import LDPlayerInstance
from pkg.util import is_email, name_user_windows
logger = logging.getLogger(__name__)


def filter_email_named_instances(instances: list[LDPlayerInstance]) -> list[LDPlayerInstance]:
    username = name_user_windows()
    logger.info(f"Username: {username}")
    email_instances = []
    for instance in instances:
        logger.info(f"LDPlayer: {instance.title}")
        args_title = instance.title.split("|")
        if len(args_title) != 2:
            logger.info(f"Skip LDPlayer: {instance.title} because it's not named correctly")
            continue
        if not is_email(args_title[0]):
            logger.info(f"Skip LDPlayer: {instance.title} because it's not named correctly")
            continue
        if args_title[1] != username:
            logger.info(f"Skip LDPlayer: {instance.title} because it's not named correctly")
            continue
        instance.title = args_title[0]
        email_instances.append(instance)
    return email_instances

def init_ldplayer():
    result = get_ldplayer_list()
    if result.is_err():
        raise result.error
    instances = result.value
    
    # Stop all running instances
    for instance in instances:
        if instance.android_started != 1:
            logger.info(f"Stopping LDPlayer {instance.title}")
            result = stop_ldplayer_by_id(instance.index)
            if result.is_err():
                raise result.error

    # Get ldplayer list again
    result = get_ldplayer_list()
    if result.is_err():
        raise result.error
    instances = result.value

    instances = filter_email_named_instances(instances)
    for instance in instances:
        logger.info(f"LDPlayer:  {instance.model_dump()} is ready")
    return instances
    

    