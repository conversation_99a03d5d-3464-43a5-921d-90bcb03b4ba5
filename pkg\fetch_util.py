import aiohttp
import asyncio
from aiohttp import <PERSON>lient<PERSON>rror, ClientResponseError, ServerTimeoutError, ClientConnectionError
from typing import Optional
import ssl
from config.config import config
import aiofiles

ssl_ctx = ssl.create_default_context()
ssl_ctx.check_hostname = False
ssl_ctx.verify_mode = ssl.CERT_NONE

DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "x-api-key": config.API_KEY
}

async def fetch(
    url: str,
    method: str = 'GET',
    params: Optional[dict] = None,
    json: Optional[dict] = None,
    headers: Optional[dict] = None,
    timeout: int = 30,
    proxy: Optional[str] = None,
    return_json: bool = True,
    ignore_status: bool = True
) -> tuple[Optional[dict], Optional[Exception]]:
    try:
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method,
                url,
                params=params,
                json=json,
                headers=headers,
                timeout=timeout,
                proxy=proxy,
                ssl=ssl_ctx
            ) as response:
                if not ignore_status:
                    response.raise_for_status()
                
                try:
                    if return_json:
                        return await response.json(), None
                    return await response.text(), None
                except Exception as e:
                    return None, Exception(f"Parse error: {str(e)}")
                    
    except ClientResponseError as e:
        error_message = f"ClientResponseError: {e.status}, URL: {e.request_info.url}"
        try:
            error_body = await e.response.text()
            error_message += f", Body: {error_body}"
        except Exception as body_read_error:
            error_message += f"Failed to read response body: {body_read_error}"
    except ServerTimeoutError:
        error_message = f"ServerTimeoutError: Request to {url} timed out"
    except ClientConnectionError:
        error_message = f"ClientConnectionError: Failed to connect to {url}"
    except ClientError as e:
        error_message = f"ClientError: {str(e)}"
    except asyncio.TimeoutError:
        error_message = f"TimeoutError: Request to {url} timed out"
    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"

    return None, Exception(error_message)

async def download_file(url: str, path_file: str):
    async with aiohttp.ClientSession() as session:
        async with session.get(url, ssl=ssl_ctx) as response:
            if response.status != 200 and response.status != 302:
                raise Exception(f"Failed to download file: {response.status}")

            async with aiofiles.open(path_file, 'wb') as file:
                while True:
                    chunk = await response.content.read(1024)
                    if not chunk:
                        break   
                    await file.write(chunk)

async def upload_file(file_path: str, signed_url: str, size: int):
    async with aiohttp.ClientSession() as session:
        with open(file_path, 'rb') as file:
            async with session.put(signed_url, ssl=ssl_ctx, data=file, headers={"Content-Length": str(size)}) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to upload file: {response.status}, {error_text}")